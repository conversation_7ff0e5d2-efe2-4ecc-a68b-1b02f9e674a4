from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database.connection import get_db
from app.schemas.recommendation import (
    Recommendation, RecommendationCreate, RecommendationRequest, 
    RecommendationResponse, RecommendationUpdate
)
from app.models.recommendation import Recommendation as RecommendationModel
from app.models.assessment import PersonalityAssessment, SkillsAssessment, InterestAssessment
from app.models.career import Career as CareerModel
from app.utils.auth import get_current_user, require_permissions
from app.models.user import User
from app.core.exceptions import NotFoundError, RecommendationError
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/generate", response_model=RecommendationResponse)
def generate_recommendations(
    request: RecommendationRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Generate career recommendations for the current user based on their assessments.
    """
    try:
        # Check if user has completed assessments
        personality_assessment = db.query(PersonalityAssessment).filter(
            PersonalityAssessment.user_id == current_user.id
        ).order_by(PersonalityAssessment.created_at.desc()).first()
        
        skills_assessment = db.query(SkillsAssessment).filter(
            SkillsAssessment.user_id == current_user.id
        ).order_by(SkillsAssessment.created_at.desc()).first()
        
        interest_assessment = db.query(InterestAssessment).filter(
            InterestAssessment.user_id == current_user.id
        ).order_by(InterestAssessment.created_at.desc()).first()
        
        if not any([personality_assessment, skills_assessment, interest_assessment]):
            raise RecommendationError(
                "No assessments found. Please complete at least one assessment first.",
                current_user.id
            )
        
        # Generate recommendations using ML engine
        recommendations = _generate_career_matches(
            db=db,
            user_id=current_user.id,
            personality_assessment=personality_assessment,
            skills_assessment=skills_assessment,
            interest_assessment=interest_assessment,
            request=request
        )
        
        # Save recommendations to database
        saved_recommendations = []
        for rec_data in recommendations:
            db_recommendation = RecommendationModel(
                user_id=current_user.id,
                career_id=rec_data["career_id"],
                overall_match_score=rec_data["overall_match_score"],
                personality_match_score=rec_data.get("personality_match_score"),
                skills_match_score=rec_data.get("skills_match_score"),
                interests_match_score=rec_data.get("interests_match_score"),
                market_demand_score=rec_data.get("market_demand_score"),
                match_factors=rec_data.get("match_factors"),
                recommended_education_path=rec_data.get("recommended_education_path"),
                recommended_skills_to_develop=rec_data.get("recommended_skills_to_develop"),
                vietnam_job_market_insights=rec_data.get("vietnam_job_market_insights"),
                recommended_regions=rec_data.get("recommended_regions"),
                expires_at=datetime.utcnow() + timedelta(days=30)
            )
            db.add(db_recommendation)
            saved_recommendations.append(db_recommendation)
        
        db.commit()
        
        # Refresh to get the full objects with relationships
        for rec in saved_recommendations:
            db.refresh(rec)
        
        logger.info(f"Generated {len(saved_recommendations)} recommendations for user {current_user.id}")
        
        return RecommendationResponse(
            recommendations=saved_recommendations,
            total_count=len(saved_recommendations),
            generated_at=datetime.utcnow()
        )
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error generating recommendations for user {current_user.id}: {e}")
        if isinstance(e, RecommendationError):
            raise e
        raise RecommendationError(f"Failed to generate recommendations: {str(e)}", current_user.id)

@router.get("/", response_model=List[Recommendation])
def get_user_recommendations(
    skip: int = 0,
    limit: int = 20,
    active_only: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get recommendations for the current user.
    """
    query = db.query(RecommendationModel).filter(
        RecommendationModel.user_id == current_user.id
    )
    
    if active_only:
        query = query.filter(
            RecommendationModel.is_active == True,
            (RecommendationModel.expires_at.is_(None)) | 
            (RecommendationModel.expires_at > datetime.utcnow())
        )
    
    recommendations = query.order_by(
        RecommendationModel.overall_match_score.desc()
    ).offset(skip).limit(limit).all()
    
    return recommendations

@router.get("/{recommendation_id}", response_model=Recommendation)
def get_recommendation(
    recommendation_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get a specific recommendation by ID.
    """
    recommendation = db.query(RecommendationModel).filter(
        RecommendationModel.id == recommendation_id,
        RecommendationModel.user_id == current_user.id
    ).first()
    
    if not recommendation:
        raise NotFoundError("Recommendation", recommendation_id)
    
    return recommendation

@router.put("/{recommendation_id}", response_model=Recommendation)
def update_recommendation(
    recommendation_id: int,
    update_data: RecommendationUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update a recommendation (user can mark as favorite, etc.).
    """
    recommendation = db.query(RecommendationModel).filter(
        RecommendationModel.id == recommendation_id,
        RecommendationModel.user_id == current_user.id
    ).first()
    
    if not recommendation:
        raise NotFoundError("Recommendation", recommendation_id)
    
    try:
        for field, value in update_data.dict(exclude_unset=True).items():
            setattr(recommendation, field, value)
        
        db.commit()
        db.refresh(recommendation)
        
        return recommendation
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating recommendation {recommendation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating recommendation"
        )

@router.delete("/{recommendation_id}")
def delete_recommendation(
    recommendation_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a recommendation.
    """
    recommendation = db.query(RecommendationModel).filter(
        RecommendationModel.id == recommendation_id,
        RecommendationModel.user_id == current_user.id
    ).first()
    
    if not recommendation:
        raise NotFoundError("Recommendation", recommendation_id)
    
    try:
        recommendation.is_active = False
        db.commit()
        
        return {"message": "Recommendation deleted successfully"}
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting recommendation {recommendation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting recommendation"
        )

def _generate_career_matches(
    db: Session,
    user_id: int,
    personality_assessment: Optional[PersonalityAssessment],
    skills_assessment: Optional[SkillsAssessment],
    interest_assessment: Optional[InterestAssessment],
    request: RecommendationRequest
) -> List[dict]:
    """
    Generate career matches using a simple algorithm.
    This is a basic implementation - in production, this would use ML models.
    """
    # Get all active careers
    careers = db.query(CareerModel).filter(CareerModel.is_active == True).all()
    
    if request.filter_by_category:
        careers = [c for c in careers if c.category.lower() == request.filter_by_category.lower()]
    
    recommendations = []
    
    for career in careers:
        match_score = _calculate_match_score(
            career, personality_assessment, skills_assessment, interest_assessment
        )
        
        if match_score >= request.min_match_score:
            recommendation = {
                "career_id": career.id,
                "overall_match_score": match_score,
                "personality_match_score": _calculate_personality_match(career, personality_assessment),
                "skills_match_score": _calculate_skills_match(career, skills_assessment),
                "interests_match_score": _calculate_interest_match(career, interest_assessment),
                "market_demand_score": _get_market_demand_score(career),
                "match_factors": _get_match_factors(career, personality_assessment, skills_assessment),
                "recommended_education_path": career.typical_education_path,
                "recommended_skills_to_develop": _get_skills_to_develop(career, skills_assessment),
                "vietnam_job_market_insights": _get_vietnam_insights(career),
                "recommended_regions": career.regions_with_high_demand or []
            }
            recommendations.append(recommendation)
    
    # Sort by match score and limit results
    recommendations.sort(key=lambda x: x["overall_match_score"], reverse=True)
    return recommendations[:request.max_recommendations]

def _calculate_match_score(career, personality_assessment, skills_assessment, interest_assessment) -> float:
    """Calculate overall match score for a career."""
    scores = []
    
    if personality_assessment:
        scores.append(_calculate_personality_match(career, personality_assessment))
    
    if skills_assessment:
        scores.append(_calculate_skills_match(career, skills_assessment))
    
    if interest_assessment:
        scores.append(_calculate_interest_match(career, interest_assessment))
    
    return sum(scores) / len(scores) if scores else 0.0

def _calculate_personality_match(career, personality_assessment) -> float:
    """Calculate personality match score."""
    if not personality_assessment:
        return 0.5  # Neutral score
    
    # Simple personality matching based on Big 5 traits
    # This would be more sophisticated in a real ML model
    match_score = 0.5  # Base score
    
    # Technology careers favor high openness and conscientiousness
    if career.category.lower() == "technology":
        if personality_assessment.openness > 3.5:
            match_score += 0.2
        if personality_assessment.conscientiousness > 3.5:
            match_score += 0.2
    
    # Business careers favor extraversion and conscientiousness
    elif career.category.lower() == "business":
        if personality_assessment.extraversion > 3.5:
            match_score += 0.2
        if personality_assessment.conscientiousness > 3.5:
            match_score += 0.2
    
    return min(match_score, 1.0)

def _calculate_skills_match(career, skills_assessment) -> float:
    """Calculate skills match score."""
    if not skills_assessment:
        return 0.5  # Neutral score
    
    # Simple skills matching - would be more sophisticated in production
    relevant_skills = {
        "mathematics": skills_assessment.mathematics,
        "english": skills_assessment.english,
        "technology": skills_assessment.technology,
        "critical_thinking": skills_assessment.critical_thinking
    }
    
    avg_skill_score = sum(relevant_skills.values()) / len(relevant_skills)
    return min(avg_skill_score / 5.0, 1.0)  # Normalize to 0-1

def _calculate_interest_match(career, interest_assessment) -> float:
    """Calculate interest match score."""
    if not interest_assessment:
        return 0.5  # Neutral score
    
    # This would match career categories with interest categories
    # For now, return a base score
    return 0.7

def _get_market_demand_score(career) -> float:
    """Get market demand score for a career."""
    demand_scores = {
        "high": 0.9,
        "medium": 0.6,
        "low": 0.3
    }
    return demand_scores.get(career.demand_in_vietnam, 0.5)

def _get_match_factors(career, personality_assessment, skills_assessment) -> dict:
    """Get detailed match factors."""
    factors = {
        "career_category": career.category,
        "salary_range": f"{career.salary_range_min_vnd:,} - {career.salary_range_max_vnd:,} VND" if career.salary_range_min_vnd else None,
        "growth_rate": f"{career.employment_growth_rate}%" if career.employment_growth_rate else None,
        "education_required": career.education_level_required
    }
    return factors

def _get_skills_to_develop(career, skills_assessment) -> List[str]:
    """Get recommended skills to develop."""
    if not career.required_skills:
        return []
    
    # Return first few required skills as development recommendations
    return career.required_skills[:3] if career.required_skills else []

def _get_vietnam_insights(career) -> str:
    """Get Vietnam-specific job market insights."""
    insights = []
    
    if career.demand_in_vietnam == "high":
        insights.append("High demand in Vietnamese job market")
    
    if career.regions_with_high_demand:
        regions = ", ".join(career.regions_with_high_demand[:3])
        insights.append(f"Strong opportunities in: {regions}")
    
    if career.average_salary_vnd:
        insights.append(f"Average salary: {career.average_salary_vnd:,} VND/month")
    
    return ". ".join(insights) if insights else "Limited market data available"
