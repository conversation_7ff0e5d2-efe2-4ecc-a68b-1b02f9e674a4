# AI-Powered Career Matching System for Vietnamese Students

An intelligent career guidance platform that helps Vietnamese students find suitable career paths based on personality traits, academic performance, interests, and local job market trends.

## Features

- Personality assessment using Big 5 model adapted for Vietnamese culture
- Skills evaluation based on academic performance
- Interest inventory with 200+ career options relevant to Vietnam
- AI recommendation engine considering multiple factors
- Vietnamese/English bilingual interface
- Integration with Vietnamese education system
- Parental involvement features
- Career recommendations with match percentages
- Detailed career profiles with Vietnam-specific information
- Study pathway recommendations
- Skill gap analysis and development suggestions

## Tech Stack

- Backend: Python with FastAPI
- Machine Learning: scikit-learn, TensorFlow
- Database: PostgreSQL
- Caching: Redis
- Task Processing: Celery
- Frontend: Material Design principles
- Deployment: Netlify, AWS, GCP compatible

## Domain Name Suggestions

1. CareerViet.AI (recommended)
2. VieCareer.com
3. CareerMatch.VN
4. SmartCareer.VN
5. PathFinder.VN

## System Architecture

```mermaid
graph TD
    A[Frontend - Vue.js/React] --> B[FastAPI Backend]
    B --> C[PostgreSQL Database]
    B --> D[Redis Cache]
    B --> E[Celery Task Queue]
    F[TensorFlow Models] --> B
    G[scikit-learn Models] --> B
    H[Vietnamese Job Data Sources] --> B
    I[Ministry of Education Data] --> B
```

## Workflow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant ML
    participant Database
    
    User->>Frontend: Access Platform
    Frontend->>Backend: Create User Profile
    Backend->>Database: Store Profile
    User->>Frontend: Complete Assessments
    Frontend->>Backend: Submit Assessment Data
    Backend->>ML: Process Assessment Results
    ML->>Backend: Generate Career Recommendations
    Backend->>Database: Store Recommendations
    Backend->>Frontend: Return Recommendations
    Frontend->>User: Display Career Matches
```

## Project Structure

```
ai-career-matching/
├── backend/
│   ├── app/
│   │   ├── api/
│   │   ├── models/
│   │   ├── schemas/
│   │   ├── database/
│   │   └── utils/
│   ├── ml/
│   │   ├── personality_model.py
│   │   ├── skills_model.py
│   │   └── recommendation_engine.py
│   └── main.py
├── frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   ├── views/
│   │   ├── assets/
│   │   └── locales/
│   └── index.html
├── data/
├── docs/
└── requirements.txt
```

## Vietnamese-Specific Adaptations

- All content available in Vietnamese and English
- Career options relevant to Vietnamese job market
- Cultural considerations in personality assessment
- Integration with Vietnamese education system grades
- Parental involvement features
- Payment methods popular in Vietnam (Momo, ZaloPay, etc.)

## Payment Methods (Vietnam)

- Momo e-wallet
- ZaloPay
- ViettelPay
- Bank transfers
- Cash on delivery for premium services

## Deployment

The application is designed to run on:
- Netlify (frontend)
- AWS/GCP (backend services)
- Docker containers for easy deployment

## SEO/GEO/AEO Features

- Multilingual SEO optimization
- Vietnam-focused geographic targeting
- Career-specific content optimization
- Mobile-responsive design
- Fast loading times with caching

## Rate Limiting

- API rate limiting to prevent abuse
- User session management with Redis
- Request throttling for free tier users