from sqlalchemy import Column, Integer, String, DateTime, Float, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database.connection import Base

class Recommendation(Base):
    __tablename__ = "recommendations"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    career_id = Column(Integer, ForeignKey("careers.id"), nullable=False)
    # Match scores
    overall_match_score = Column(Float, nullable=False)  # Overall match percentage (0-100)
    personality_match_score = Column(Float, nullable=True)  # Personality match score (0-100)
    skills_match_score = Column(Float, nullable=True)  # Skills match score (0-100)
    interests_match_score = Column(Float, nullable=True)  # Interests match score (0-100)
    market_demand_score = Column(Float, nullable=True)  # Market demand score (0-100)
    # Detailed breakdown
    match_factors = Column(JSON, nullable=True)  # Detailed breakdown of match factors
    # Career pathway recommendations
    recommended_education_path = Column(Text, nullable=True)  # Recommended education path
    recommended_skills_to_develop = Column(JSON, nullable=True)  # Skills to develop
    # Vietnamese specific recommendations
    vietnam_job_market_insights = Column(Text, nullable=True)  # Insights about job market in Vietnam
    recommended_regions = Column(JSON, nullable=True)  # Recommended regions in Vietnam
    # Metadata
    generated_at = Column(DateTime, server_default=func.now())
    expires_at = Column(DateTime, nullable=True)  # When recommendation expires
    is_active = Column(Boolean, default=True)
    
    # Relationships
    user = relationship("User", back_populates="recommendations")
    career = relationship("Career", back_populates="recommendations")

class RecommendationFeedback(Base):
    __tablename__ = "recommendation_feedback"
    
    id = Column(Integer, primary_key=True, index=True)
    recommendation_id = Column(Integer, ForeignKey("recommendations.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    # Feedback scores
    helpfulness_score = Column(Integer, nullable=True)  # 1-5 scale
    accuracy_score = Column(Integer, nullable=True)  # 1-5 scale
    # Feedback details
    feedback_text = Column(Text, nullable=True)  # User feedback text
    would_consider_career = Column(Boolean, nullable=True)  # Whether user would consider this career
    additional_comments = Column(Text, nullable=True)  # Additional comments
    # Metadata
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relationships
    recommendation = relationship("Recommendation")
    user = relationship("User")