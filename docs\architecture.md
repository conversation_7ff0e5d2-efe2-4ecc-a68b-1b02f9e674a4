# System Architecture

## Overview

The AI-powered career matching system consists of several interconnected components that work together to provide personalized career recommendations for Vietnamese students.

## Architecture Diagram

```mermaid
graph TD
    A[Frontend - Vue.js/React] --> B[FastAPI Backend]
    B --> C[PostgreSQL Database]
    B --> D[Redis Cache]
    B --> E[Celery Task Queue]
    F[TensorFlow Models] --> B
    G[scikit-learn Models] --> B
    H[Vietnamese Job Data Sources] --> B
    I[Ministry of Education Data] --> B
    J[Payment Gateway] --> B
    K[Authentication Service] --> B
```

## Component Descriptions

### Frontend
- Built with Vue.js or React
- Material Design principles implementation
- Vietnamese/English language support
- Responsive design for all devices

### Backend (FastAPI)
- RESTful API endpoints
- Authentication and authorization
- Business logic implementation
- Integration with machine learning models
- Payment processing

### Database (PostgreSQL)
- User profiles and assessment data
- Career information and job market data
- Recommendation results
- Payment transactions

### Cache (Redis)
- Session management
- Frequently accessed data caching
- Rate limiting implementation

### Task Queue (Celery)
- Background processing of assessments
- Machine learning model training
- Data synchronization tasks
- Email notifications

### Machine Learning Models
- Personality assessment model (Big 5 adapted for Vietnamese culture)
- Skills evaluation model
- Recommendation engine
- Job market trend analysis

### Data Sources
- Vietnamese job posting websites (VietnamWorks, CareerViet)
- Ministry of Education curriculum standards
- University admission requirements
- Industry salary surveys

### Payment Gateway
- Integration with Vietnamese payment methods
- Momo, ZaloPay, ViettelPay
- Bank transfers
- Subscription management