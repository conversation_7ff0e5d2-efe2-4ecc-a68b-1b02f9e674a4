from pydantic import BaseSettings, validator
from typing import List, Union
import os

class Settings(BaseSettings):
    PROJECT_NAME: str = "AI Career Matching System"
    API_V1_STR: str = "/api/v1"
    
    # Database settings
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "careerviet"
    POSTGRES_PASSWORD: str = "careerviet"
    POSTGRES_DB: str = "careerviet"
    DATABASE_URL: str = f"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_SERVER}/{POSTGRES_DB}"
    
    # Redis settings
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_URL: str = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
    
    # Celery settings
    CELERY_BROKER_URL: str = REDIS_URL
    CELERY_RESULT_BACKEND: str = REDIS_URL
    
    # Security settings
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALGORITHM: str = "HS256"
    
    # CORS settings
    BACKEND_CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:8000"]
    
    # Payment settings
    MOMO_API_KEY: str = ""
    ZALOPAY_API_KEY: str = ""
    VIETTELPAY_API_KEY: str = ""
    
    # Rate limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_TIME_WINDOW: int = 3600  # in seconds
    
    # SEO settings
    SEO_TITLE: str = "AI Career Matching System for Vietnamese Students"
    SEO_DESCRIPTION: str = "Find your perfect career path with our AI-powered system designed for Vietnamese students"
    SEO_KEYWORDS: str = "career, matching, AI, Vietnam, students, jobs"
    
    # GEO settings
    GEO_DEFAULT_COUNTRY: str = "VN"
    GEO_DEFAULT_LANGUAGE: str = "vi"
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    class Config:
        case_sensitive = True
        env_file = ".env"

settings = Settings()