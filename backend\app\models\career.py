from sqlalchemy import Column, Integer, String, Text, Float, DateTime, <PERSON>olean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database.connection import Base
from typing import List, Optional

class Career(Base):
    __tablename__ = "careers"
    
    id = Column(Integer, primary_key=True, index=True)
    name_en = Column(String, nullable=False)  # English name
    name_vi = Column(String, nullable=False)  # Vietnamese name
    description_en = Column(Text, nullable=True)  # English description
    description_vi = Column(Text, nullable=True)  # Vietnamese description
    category = Column(String, nullable=False)  # Career category
    subcategory = Column(String, nullable=True)  # Career subcategory
    # Job market data for Vietnam
    average_salary_vnd = Column(Integer, nullable=True)  # Average salary in VND
    salary_range_min_vnd = Column(Integer, nullable=True)  # Minimum salary in VND
    salary_range_max_vnd = Column(Integer, nullable=True)  # Maximum salary in VND
    employment_growth_rate = Column(Float, nullable=True)  # Employment growth rate
    job_openings_count = Column(Integer, nullable=True)  # Current job openings
    # Education requirements
    education_level_required = Column(String, nullable=True)  # Required education level
    typical_education_path = Column(Text, nullable=True)  # Typical education path
    # Skills requirements
    required_skills = Column(JSON, nullable=True)  # List of required skills
    # Personality traits match
    personality_match_openness = Column(Float, nullable=True)
    personality_match_conscientiousness = Column(Float, nullable=True)
    personality_match_extraversion = Column(Float, nullable=True)
    personality_match_agreeableness = Column(Float, nullable=True)
    personality_match_neuroticism = Column(Float, nullable=True)
    # Work environment
    work_environment = Column(Text, nullable=True)  # Work environment description
    work_schedule = Column(String, nullable=True)  # Work schedule (full-time, part-time, etc.)
    work_setting = Column(String, nullable=True)  # Work setting (office, remote, etc.)
    # Career pathways
    advancement_opportunities = Column(Text, nullable=True)  # Advancement opportunities
    related_careers = Column(JSON, nullable=True)  # Related careers
    # Vietnamese specific data
    demand_in_vietnam = Column(String, nullable=True)  # Demand level in Vietnam (high, medium, low)
    regions_with_high_demand = Column(JSON, nullable=True)  # Regions with high demand in Vietnam
    # Metadata
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relationships
    career_interests = relationship("CareerInterest", back_populates="career")
    recommendations = relationship("Recommendation", back_populates="career")

class CareerInterest(Base):
    __tablename__ = "career_interests"
    
    id = Column(Integer, primary_key=True, index=True)
    career_id = Column(Integer, ForeignKey("careers.id"), nullable=False)
    interest_category = Column(String, nullable=False)  # Interest category (realistic, investigative, etc.)
    interest_score = Column(Float, nullable=False)  # Score for this interest category (0-100)
    created_at = Column(DateTime, server_default=func.now())
    
    # Relationship
    career = relationship("Career", back_populates="career_interests")