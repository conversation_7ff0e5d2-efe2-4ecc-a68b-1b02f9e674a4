import logging
import logging.config
import sys
from pathlib import Path
from typing import Dict, Any
import json
from datetime import datetime

def setup_logging(
    log_level: str = "INFO",
    log_format: str = "json",
    log_file: str = None
) -> None:
    """
    Set up logging configuration for the application.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Format type ('json' or 'text')
        log_file: Optional log file path
    """
    
    # Create logs directory if it doesn't exist
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure logging based on format preference
    if log_format.lower() == "json":
        logging_config = get_json_logging_config(log_level, log_file)
    else:
        logging_config = get_text_logging_config(log_level, log_file)
    
    logging.config.dictConfig(logging_config)
    
    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info("Logging system initialized", extra={
        "log_level": log_level,
        "log_format": log_format,
        "log_file": log_file
    })

def get_json_logging_config(log_level: str, log_file: str = None) -> Dict[str, Any]:
    """Get JSON-formatted logging configuration."""
    
    handlers = {
        "console": {
            "class": "logging.StreamHandler",
            "level": log_level,
            "formatter": "json",
            "stream": "ext://sys.stdout"
        }
    }
    
    if log_file:
        handlers["file"] = {
            "class": "logging.handlers.RotatingFileHandler",
            "level": log_level,
            "formatter": "json",
            "filename": log_file,
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5
        }
    
    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "()": "backend.app.core.logging.JSONFormatter"
            }
        },
        "handlers": handlers,
        "root": {
            "level": log_level,
            "handlers": list(handlers.keys())
        },
        "loggers": {
            "uvicorn": {
                "level": "INFO",
                "handlers": list(handlers.keys()),
                "propagate": False
            },
            "uvicorn.error": {
                "level": "INFO",
                "handlers": list(handlers.keys()),
                "propagate": False
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": list(handlers.keys()),
                "propagate": False
            },
            "sqlalchemy.engine": {
                "level": "WARNING",
                "handlers": list(handlers.keys()),
                "propagate": False
            }
        }
    }

def get_text_logging_config(log_level: str, log_file: str = None) -> Dict[str, Any]:
    """Get text-formatted logging configuration."""
    
    handlers = {
        "console": {
            "class": "logging.StreamHandler",
            "level": log_level,
            "formatter": "standard",
            "stream": "ext://sys.stdout"
        }
    }
    
    if log_file:
        handlers["file"] = {
            "class": "logging.handlers.RotatingFileHandler",
            "level": log_level,
            "formatter": "standard",
            "filename": log_file,
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5
        }
    
    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "standard": {
                "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            }
        },
        "handlers": handlers,
        "root": {
            "level": log_level,
            "handlers": list(handlers.keys())
        },
        "loggers": {
            "uvicorn": {
                "level": "INFO",
                "handlers": list(handlers.keys()),
                "propagate": False
            },
            "sqlalchemy.engine": {
                "level": "WARNING",
                "handlers": list(handlers.keys()),
                "propagate": False
            }
        }
    }

class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        
        # Create base log entry
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields if present
        if hasattr(record, 'user_id'):
            log_entry["user_id"] = record.user_id
        if hasattr(record, 'request_id'):
            log_entry["request_id"] = record.request_id
        if hasattr(record, 'ip_address'):
            log_entry["ip_address"] = record.ip_address
        
        # Add any other extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'getMessage', 'exc_info', 
                          'exc_text', 'stack_info']:
                if not key.startswith('_'):
                    log_entry[key] = value
        
        return json.dumps(log_entry, ensure_ascii=False)

def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the specified name."""
    return logging.getLogger(name)

# Convenience function for request logging
def log_request(logger: logging.Logger, method: str, path: str, status_code: int, 
                duration: float, user_id: int = None, ip_address: str = None):
    """Log HTTP request with structured data."""
    logger.info(
        f"{method} {path} - {status_code}",
        extra={
            "http_method": method,
            "http_path": path,
            "http_status_code": status_code,
            "duration_ms": round(duration * 1000, 2),
            "user_id": user_id,
            "ip_address": ip_address
        }
    )

# Convenience function for error logging
def log_error(logger: logging.Logger, error: Exception, context: Dict[str, Any] = None):
    """Log error with structured context data."""
    extra_data = {
        "error_type": type(error).__name__,
        "error_message": str(error)
    }
    
    if context:
        extra_data.update(context)
    
    logger.error(f"Error occurred: {error}", extra=extra_data, exc_info=True)
