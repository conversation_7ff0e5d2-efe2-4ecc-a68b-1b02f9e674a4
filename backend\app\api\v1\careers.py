from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database.connection import get_db
from app.schemas.career import Career, CareerCreate, CareerUpdate, CareerSearch
from app.models.career import Career as CareerModel
from app.utils.auth import get_current_user, get_current_admin_user
from app.models.user import User
from app.core.exceptions import NotFoundError, AuthorizationError
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/", response_model=List[Career])
def get_careers(
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = None,
    search: Optional[str] = None,
    demand_level: Optional[str] = Query(None, regex="^(high|medium|low)$"),
    min_salary: Optional[int] = None,
    max_salary: Optional[int] = None,
    region: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Retrieve careers with optional filtering.
    """
    query = db.query(CareerModel).filter(CareerModel.is_active == True)
    
    # Apply filters
    if category:
        query = query.filter(CareerModel.category.ilike(f"%{category}%"))
    
    if search:
        query = query.filter(
            (CareerModel.name_en.ilike(f"%{search}%")) |
            (CareerModel.name_vi.ilike(f"%{search}%")) |
            (CareerModel.description_en.ilike(f"%{search}%")) |
            (CareerModel.description_vi.ilike(f"%{search}%"))
        )
    
    if demand_level:
        query = query.filter(CareerModel.demand_in_vietnam == demand_level)
    
    if min_salary:
        query = query.filter(CareerModel.average_salary_vnd >= min_salary)
    
    if max_salary:
        query = query.filter(CareerModel.average_salary_vnd <= max_salary)
    
    if region:
        query = query.filter(CareerModel.regions_with_high_demand.contains([region]))
    
    careers = query.offset(skip).limit(limit).all()
    return careers

@router.get("/{career_id}", response_model=Career)
def get_career(
    career_id: int,
    db: Session = Depends(get_db)
):
    """
    Get a specific career by ID.
    """
    career = db.query(CareerModel).filter(
        CareerModel.id == career_id,
        CareerModel.is_active == True
    ).first()
    
    if not career:
        raise NotFoundError("Career", career_id)
    
    return career

@router.post("/", response_model=Career)
def create_career(
    career: CareerCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Create a new career (admin only).
    """
    try:
        db_career = CareerModel(**career.dict())
        db.add(db_career)
        db.commit()
        db.refresh(db_career)
        
        logger.info(f"Career created: {db_career.name_en} (ID: {db_career.id})")
        return db_career
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating career: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating career"
        )

@router.put("/{career_id}", response_model=Career)
def update_career(
    career_id: int,
    career_update: CareerUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Update a career (admin only).
    """
    db_career = db.query(CareerModel).filter(CareerModel.id == career_id).first()
    if not db_career:
        raise NotFoundError("Career", career_id)
    
    try:
        # Update career fields
        for field, value in career_update.dict(exclude_unset=True).items():
            setattr(db_career, field, value)
        
        db.commit()
        db.refresh(db_career)
        
        logger.info(f"Career updated: {db_career.name_en} (ID: {db_career.id})")
        return db_career
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating career {career_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating career"
        )

@router.delete("/{career_id}")
def delete_career(
    career_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Delete a career (admin only) - soft delete by setting is_active to False.
    """
    db_career = db.query(CareerModel).filter(CareerModel.id == career_id).first()
    if not db_career:
        raise NotFoundError("Career", career_id)
    
    try:
        db_career.is_active = False
        db.commit()
        
        logger.info(f"Career deleted: {db_career.name_en} (ID: {db_career.id})")
        return {"message": "Career deleted successfully"}
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting career {career_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting career"
        )

@router.get("/search/", response_model=List[Career])
def search_careers(
    q: str = Query(..., min_length=1, description="Search query"),
    category: Optional[str] = None,
    limit: int = Query(20, le=100),
    db: Session = Depends(get_db)
):
    """
    Search careers by name, description, or skills.
    """
    query = db.query(CareerModel).filter(CareerModel.is_active == True)
    
    # Search in multiple fields
    search_filter = (
        (CareerModel.name_en.ilike(f"%{q}%")) |
        (CareerModel.name_vi.ilike(f"%{q}%")) |
        (CareerModel.description_en.ilike(f"%{q}%")) |
        (CareerModel.description_vi.ilike(f"%{q}%")) |
        (CareerModel.required_skills.contains([q]))
    )
    
    query = query.filter(search_filter)
    
    if category:
        query = query.filter(CareerModel.category.ilike(f"%{category}%"))
    
    careers = query.limit(limit).all()
    return careers

@router.get("/categories/", response_model=List[str])
def get_career_categories(db: Session = Depends(get_db)):
    """
    Get all unique career categories.
    """
    categories = db.query(CareerModel.category).filter(
        CareerModel.is_active == True
    ).distinct().all()
    
    return [category[0] for category in categories if category[0]]

@router.get("/regions/", response_model=List[str])
def get_career_regions(db: Session = Depends(get_db)):
    """
    Get all regions with high demand for careers.
    """
    careers = db.query(CareerModel.regions_with_high_demand).filter(
        CareerModel.is_active == True,
        CareerModel.regions_with_high_demand.isnot(None)
    ).all()
    
    regions = set()
    for career in careers:
        if career.regions_with_high_demand:
            regions.update(career.regions_with_high_demand)
    
    return sorted(list(regions))

@router.get("/{career_id}/similar/", response_model=List[Career])
def get_similar_careers(
    career_id: int,
    limit: int = Query(5, le=20),
    db: Session = Depends(get_db)
):
    """
    Get careers similar to the specified career.
    """
    career = db.query(CareerModel).filter(
        CareerModel.id == career_id,
        CareerModel.is_active == True
    ).first()
    
    if not career:
        raise NotFoundError("Career", career_id)
    
    # Find similar careers based on category and subcategory
    similar_query = db.query(CareerModel).filter(
        CareerModel.id != career_id,
        CareerModel.is_active == True
    )
    
    # Prioritize same subcategory, then same category
    if career.subcategory:
        similar_query = similar_query.filter(
            (CareerModel.subcategory == career.subcategory) |
            (CareerModel.category == career.category)
        ).order_by(
            (CareerModel.subcategory == career.subcategory).desc(),
            (CareerModel.category == career.category).desc()
        )
    else:
        similar_query = similar_query.filter(
            CareerModel.category == career.category
        )
    
    similar_careers = similar_query.limit(limit).all()
    return similar_careers

@router.get("/stats/overview/")
def get_career_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Get career statistics overview (admin only).
    """
    total_careers = db.query(CareerModel).filter(CareerModel.is_active == True).count()
    
    # Count by category
    categories = db.query(
        CareerModel.category,
        db.func.count(CareerModel.id).label('count')
    ).filter(
        CareerModel.is_active == True
    ).group_by(CareerModel.category).all()
    
    # Count by demand level
    demand_levels = db.query(
        CareerModel.demand_in_vietnam,
        db.func.count(CareerModel.id).label('count')
    ).filter(
        CareerModel.is_active == True,
        CareerModel.demand_in_vietnam.isnot(None)
    ).group_by(CareerModel.demand_in_vietnam).all()
    
    # Average salary
    avg_salary = db.query(
        db.func.avg(CareerModel.average_salary_vnd)
    ).filter(
        CareerModel.is_active == True,
        CareerModel.average_salary_vnd.isnot(None)
    ).scalar()
    
    return {
        "total_careers": total_careers,
        "categories": [{"name": cat[0], "count": cat[1]} for cat in categories],
        "demand_levels": [{"level": level[0], "count": level[1]} for level in demand_levels],
        "average_salary_vnd": int(avg_salary) if avg_salary else None
    }
