from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import uuid
from datetime import datetime, timedelta
import logging

from app.database.connection import get_db
from app.schemas.payment import (
    Payment, PaymentCreate, PaymentUpdate, PaymentRequest, PaymentResponse,
    PaymentStatus, PaymentMethod, SubscriptionTier, Subscription
)
from app.models.payment import Payment as PaymentModel, Subscription as SubscriptionModel
from app.utils.auth import get_current_user, get_current_admin_user
from app.models.user import User
from app.core.exceptions import NotFoundError, PaymentError, FileUploadError

logger = logging.getLogger(__name__)
router = APIRouter()

# Configuration
UPLOAD_DIR = "uploads/payment_proofs"
QR_CODE_PATH = "bank.jfif"
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB
ALLOWED_EXTENSIONS = {".jpg", ".jpeg", ".png", ".pdf"}

# Ensure upload directory exists
os.makedirs(UPLOAD_DIR, exist_ok=True)

@router.get("/qr-code")
def get_payment_qr_code():
    """
    Get the Vietnamese bank QR code for payment.
    """
    if not os.path.exists(QR_CODE_PATH):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="QR code image not found"
        )
    
    return FileResponse(
        path=QR_CODE_PATH,
        media_type="image/jpeg",
        filename="payment_qr_code.jpg"
    )

@router.post("/create", response_model=PaymentResponse)
def create_payment(
    payment_request: PaymentRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new payment request for Vietnamese users.
    """
    try:
        # Create payment record
        payment = PaymentModel(
            user_id=current_user.id,
            amount=payment_request.amount,
            payment_method=payment_request.payment_method,
            subscription_tier=payment_request.subscription_tier,
            description=payment_request.description,
            payment_status=PaymentStatus.PENDING
        )
        
        db.add(payment)
        db.commit()
        db.refresh(payment)
        
        logger.info(f"Payment created: {payment.id} for user {current_user.id}")
        
        # For Vietnamese bank transfer, provide QR code instructions
        if payment_request.payment_method == PaymentMethod.BANK_TRANSFER:
            message = (
                "Payment created successfully. Please scan the QR code to complete payment, "
                "then upload your payment proof using the upload endpoint."
            )
        else:
            message = "Payment created successfully. Please complete payment and upload proof."
        
        return PaymentResponse(
            payment_id=payment.id,
            status=PaymentStatus.PENDING,
            message=message
        )
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating payment for user {current_user.id}: {e}")
        raise PaymentError(f"Failed to create payment: {str(e)}")

@router.post("/{payment_id}/upload-proof")
def upload_payment_proof(
    payment_id: int,
    file: UploadFile = File(...),
    notes: Optional[str] = Form(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Upload payment proof for a pending payment.
    """
    # Verify payment exists and belongs to user
    payment = db.query(PaymentModel).filter(
        PaymentModel.id == payment_id,
        PaymentModel.user_id == current_user.id
    ).first()
    
    if not payment:
        raise NotFoundError("Payment", payment_id)
    
    if payment.payment_status != PaymentStatus.PENDING:
        raise PaymentError(
            f"Cannot upload proof for payment with status: {payment.payment_status}",
            str(payment_id)
        )
    
    # Validate file
    if file.size > MAX_FILE_SIZE:
        raise FileUploadError(
            "File too large. Maximum size is 5MB.",
            file.filename,
            file.size
        )
    
    file_extension = os.path.splitext(file.filename)[1].lower()
    if file_extension not in ALLOWED_EXTENSIONS:
        raise FileUploadError(
            f"Invalid file type. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}",
            file.filename
        )
    
    try:
        # Generate unique filename
        unique_filename = f"{payment_id}_{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(UPLOAD_DIR, unique_filename)
        
        # Save file
        with open(file_path, "wb") as buffer:
            content = file.file.read()
            buffer.write(content)
        
        # Update payment record
        payment.payment_proof_filename = unique_filename
        payment.payment_proof_notes = notes
        payment.payment_status = PaymentStatus.PENDING  # Still pending admin verification
        
        db.commit()
        
        logger.info(f"Payment proof uploaded for payment {payment_id}")
        
        return {
            "message": "Payment proof uploaded successfully. Your payment is now under review.",
            "filename": unique_filename,
            "status": "pending_verification"
        }
        
    except Exception as e:
        logger.error(f"Error uploading payment proof for payment {payment_id}: {e}")
        raise FileUploadError(f"Failed to upload file: {str(e)}", file.filename)

@router.get("/", response_model=List[Payment])
def get_user_payments(
    skip: int = 0,
    limit: int = 20,
    status_filter: Optional[PaymentStatus] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get payments for the current user.
    """
    query = db.query(PaymentModel).filter(PaymentModel.user_id == current_user.id)
    
    if status_filter:
        query = query.filter(PaymentModel.payment_status == status_filter)
    
    payments = query.order_by(PaymentModel.created_at.desc()).offset(skip).limit(limit).all()
    return payments

@router.get("/{payment_id}", response_model=Payment)
def get_payment(
    payment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get a specific payment by ID.
    """
    payment = db.query(PaymentModel).filter(
        PaymentModel.id == payment_id,
        PaymentModel.user_id == current_user.id
    ).first()
    
    if not payment:
        raise NotFoundError("Payment", payment_id)
    
    return payment

@router.get("/admin/pending", response_model=List[Payment])
def get_pending_payments(
    skip: int = 0,
    limit: int = 50,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Get all pending payments for admin review.
    """
    payments = db.query(PaymentModel).filter(
        PaymentModel.payment_status == PaymentStatus.PENDING,
        PaymentModel.payment_proof_filename.isnot(None)
    ).order_by(PaymentModel.created_at.asc()).offset(skip).limit(limit).all()
    
    return payments

@router.get("/admin/proof/{payment_id}")
def get_payment_proof(
    payment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Get payment proof file for admin review.
    """
    payment = db.query(PaymentModel).filter(PaymentModel.id == payment_id).first()
    
    if not payment:
        raise NotFoundError("Payment", payment_id)
    
    if not payment.payment_proof_filename:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No payment proof uploaded for this payment"
        )
    
    file_path = os.path.join(UPLOAD_DIR, payment.payment_proof_filename)
    
    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment proof file not found"
        )
    
    return FileResponse(
        path=file_path,
        filename=f"payment_proof_{payment_id}_{payment.payment_proof_filename}"
    )

@router.put("/admin/{payment_id}/verify")
def verify_payment(
    payment_id: int,
    action: str = Form(..., regex="^(approve|reject)$"),
    admin_notes: Optional[str] = Form(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Approve or reject a payment (admin only).
    """
    payment = db.query(PaymentModel).filter(PaymentModel.id == payment_id).first()
    
    if not payment:
        raise NotFoundError("Payment", payment_id)
    
    try:
        if action == "approve":
            payment.payment_status = PaymentStatus.COMPLETED
            payment.completed_at = datetime.utcnow()
            
            # Create or update subscription
            _create_or_update_subscription(db, payment)
            
            message = "Payment approved and subscription activated"
            
        else:  # reject
            payment.payment_status = PaymentStatus.FAILED
            message = "Payment rejected"
        
        payment.admin_notes = admin_notes
        payment.verified_by = current_user.id
        payment.verified_at = datetime.utcnow()
        
        db.commit()
        
        logger.info(f"Payment {payment_id} {action}ed by admin {current_user.id}")
        
        return {"message": message, "status": payment.payment_status}
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error verifying payment {payment_id}: {e}")
        raise PaymentError(f"Failed to verify payment: {str(e)}", str(payment_id))

@router.get("/subscription/status", response_model=Subscription)
def get_subscription_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get current user's subscription status.
    """
    subscription = db.query(SubscriptionModel).filter(
        SubscriptionModel.user_id == current_user.id,
        SubscriptionModel.is_active == True
    ).order_by(SubscriptionModel.created_at.desc()).first()
    
    if not subscription:
        # Return default free subscription
        return Subscription(
            id=0,
            user_id=current_user.id,
            tier=SubscriptionTier.FREE,
            is_active=True,
            created_at=current_user.created_at,
            updated_at=current_user.updated_at
        )
    
    return subscription

def _create_or_update_subscription(db: Session, payment: PaymentModel):
    """Create or update user subscription based on payment."""
    # Deactivate existing subscriptions
    db.query(SubscriptionModel).filter(
        SubscriptionModel.user_id == payment.user_id,
        SubscriptionModel.is_active == True
    ).update({"is_active": False})
    
    # Calculate subscription duration based on tier
    duration_days = {
        SubscriptionTier.BASIC: 30,
        SubscriptionTier.PREMIUM: 30
    }.get(payment.subscription_tier, 30)
    
    # Create new subscription
    subscription = SubscriptionModel(
        user_id=payment.user_id,
        payment_id=payment.id,
        tier=payment.subscription_tier,
        starts_at=datetime.utcnow(),
        expires_at=datetime.utcnow() + timedelta(days=duration_days),
        is_active=True
    )
    
    db.add(subscription)
    
    # Update user premium status
    user = db.query(User).filter(User.id == payment.user_id).first()
    if user:
        user.is_premium = payment.subscription_tier in [SubscriptionTier.BASIC, SubscriptionTier.PREMIUM]
        user.subscription_tier = payment.subscription_tier
