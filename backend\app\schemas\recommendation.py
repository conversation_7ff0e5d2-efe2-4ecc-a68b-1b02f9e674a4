from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime

class RecommendationBase(BaseModel):
    career_id: int
    overall_match_score: float
    personality_match_score: Optional[float] = None
    skills_match_score: Optional[float] = None
    interests_match_score: Optional[float] = None
    market_demand_score: Optional[float] = None

class RecommendationCreate(RecommendationBase):
    match_factors: Optional[Dict[str, Any]] = None
    recommended_skills_to_develop: Optional[List[str]] = None
    recommended_regions: Optional[List[str]] = None

class RecommendationUpdate(RecommendationBase):
    pass

class RecommendationInDBBase(RecommendationBase):
    id: int
    user_id: int
    match_factors: Optional[Dict[str, Any]] = None
    recommended_education_path: Optional[str] = None
    recommended_skills_to_develop: Optional[List[str]] = None
    vietnam_job_market_insights: Optional[str] = None
    recommended_regions: Optional[List[str]] = None
    generated_at: datetime
    expires_at: Optional[datetime] = None
    is_active: bool

    class Config:
        orm_mode = True

class Recommendation(RecommendationInDBBase):
    career: Optional[Any] = None  # We'll define this properly in the API layer

class RecommendationFeedbackBase(BaseModel):
    recommendation_id: int
    helpfulness_score: Optional[int] = None
    accuracy_score: Optional[int] = None
    feedback_text: Optional[str] = None
    would_consider_career: Optional[bool] = None
    additional_comments: Optional[str] = None

class RecommendationFeedbackCreate(RecommendationFeedbackBase):
    pass

class RecommendationFeedbackUpdate(RecommendationFeedbackBase):
    pass

class RecommendationFeedbackInDBBase(RecommendationFeedbackBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class RecommendationFeedback(RecommendationFeedbackInDBBase):
    pass

class RecommendationRequest(BaseModel):
    # Request parameters for generating recommendations
    include_detailed_analysis: Optional[bool] = True
    max_recommendations: Optional[int] = 10
    filter_by_category: Optional[str] = None
    min_match_score: Optional[float] = 0.0
    consider_job_market: Optional[bool] = True

class RecommendationResponse(BaseModel):
    recommendations: List[Recommendation]
    total_count: int
    generated_at: datetime

class CareerMatchDetail(BaseModel):
    career_id: int
    career_name_en: str
    career_name_vi: str
    overall_match_score: float
    personality_match_score: Optional[float] = None
    skills_match_score: Optional[float] = None
    interests_match_score: Optional[float] = None
    market_demand_score: Optional[float] = None
    match_factors: Optional[Dict[str, Any]] = None
    recommended_education_path: Optional[str] = None
    recommended_skills_to_develop: Optional[List[str]] = None
    vietnam_job_market_insights: Optional[str] = None
    salary_range_vnd: Optional[Dict[str, int]] = None
    demand_in_vietnam: Optional[str] = None
    recommended_regions: Optional[List[str]] = None

class SkillGapAnalysis(BaseModel):
    current_skills: List[str]
    required_skills: List[str]
    missing_skills: List[str]
    skill_development_plan: List[Dict[str, Any]]
    recommended_courses: List[str]
    estimated_completion_time: str

class CareerPathway(BaseModel):
    career_id: int
    career_name_en: str
    career_name_vi: str
    current_step: str
    next_steps: List[str]
    required_education: str
    required_skills: List[str]
    timeline: str
    potential_employers: List[str]