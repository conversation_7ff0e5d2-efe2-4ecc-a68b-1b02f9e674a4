from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2Pass<PERSON>R<PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session
from app.database.connection import get_db
from app.schemas.user import User<PERSON><PERSON>, User, Token, Password<PERSON>hange, PasswordResetRequest, PasswordResetConfirm
from app.models.user import User as UserModel
from app.core.security import create_access_token, verify_password, get_password_hash
from app.core.config import settings
from datetime import timedelta
from typing import Optional

router = APIRouter()

@router.post("/register", response_model=User)
def register_user(user: UserCreate, db: Session = Depends(get_db)):
    # Check if user already exists
    db_user = db.query(UserModel).filter(UserModel.email == user.email).first()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    db_user = db.query(UserModel).filter(UserModel.username == user.username).first()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already taken"
        )
    
    # Create new user
    hashed_password = get_password_hash(user.password)
    db_user = UserModel(
        email=user.email,
        username=user.username,
        full_name=user.full_name,
        hashed_password=hashed_password,
        date_of_birth=user.date_of_birth,
        gender=user.gender,
        school=user.school,
        grade_level=user.grade_level,
        parent_email=user.parent_email
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return db_user

@router.post("/login", response_model=Token)
def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    # Authenticate user
    user = db.query(UserModel).filter(UserModel.username == form_data.username).first()
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create access token
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    
    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/password/change")
def change_password(password_data: PasswordChange, db: Session = Depends(get_db), current_user: UserModel = Depends(get_current_user)):
    # Verify current password
    if not verify_password(password_data.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )
    
    # Update password
    current_user.hashed_password = get_password_hash(password_data.new_password)
    db.commit()
    
    return {"message": "Password updated successfully"}

@router.post("/password/reset/request")
def request_password_reset(reset_data: PasswordResetRequest, db: Session = Depends(get_db)):
    # Find user by email
    user = db.query(UserModel).filter(UserModel.email == reset_data.email).first()
    if not user:
        # Don't reveal if user exists or not
        return {"message": "If email exists, password reset instructions have been sent"}
    
    # Generate reset token (in production, use a secure method)
    # Send email with reset link
    
    return {"message": "If email exists, password reset instructions have been sent"}

@router.post("/password/reset/confirm")
def confirm_password_reset(reset_data: PasswordResetConfirm, db: Session = Depends(get_db)):
    # Verify token and reset password
    # This is a simplified implementation
    
    return {"message": "Password reset successfully"}

def get_current_user(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = db.query(UserModel).filter(UserModel.username == username).first()
    if user is None:
        raise credentials_exception
    
    return user