import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
import joblib
import logging
from typing import Dict, List, Tuple, Optional
import os

logger = logging.getLogger(__name__)

class PersonalityModel:
    """
    Machine learning model for personality assessment and career matching.
    Uses Big 5 personality traits adapted for Vietnamese culture.
    """
    
    def __init__(self, model_path: str = "models/personality_model.pkl"):
        self.model_path = model_path
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False
        
        # Big 5 personality traits
        self.traits = [
            'openness',
            'conscientiousness', 
            'extraversion',
            'agreeableness',
            'neuroticism'
        ]
        
        # Vietnamese cultural adaptations for personality questions
        self.vietnamese_cultural_weights = {
            'family_importance': 0.3,
            'respect_for_authority': 0.2,
            'group_harmony': 0.25,
            'educational_achievement': 0.25
        }
    
    def load_model(self) -> bool:
        """Load pre-trained model from disk."""
        try:
            if os.path.exists(self.model_path):
                model_data = joblib.load(self.model_path)
                self.model = model_data['model']
                self.scaler = model_data['scaler']
                self.is_trained = True
                logger.info("Personality model loaded successfully")
                return True
            else:
                logger.warning(f"Model file not found: {self.model_path}")
                return False
        except Exception as e:
            logger.error(f"Error loading personality model: {e}")
            return False
    
    def save_model(self) -> bool:
        """Save trained model to disk."""
        try:
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'traits': self.traits
            }
            joblib.save(model_data, self.model_path)
            logger.info(f"Personality model saved to {self.model_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving personality model: {e}")
            return False
    
    def prepare_training_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare synthetic training data for personality assessment.
        In production, this would use real assessment data.
        """
        # Generate synthetic data for demonstration
        np.random.seed(42)
        n_samples = 1000
        
        # Simulate personality questionnaire responses (1-5 scale)
        # Each person answers 50 questions across 5 traits
        questions_per_trait = 10
        total_questions = len(self.traits) * questions_per_trait
        
        # Generate responses with some correlation structure
        responses = []
        personality_scores = []
        
        for i in range(n_samples):
            # Generate base personality scores (0-5 scale)
            base_scores = np.random.normal(3, 0.8, len(self.traits))
            base_scores = np.clip(base_scores, 1, 5)
            
            # Generate questionnaire responses based on personality scores
            person_responses = []
            for trait_idx, trait_score in enumerate(base_scores):
                # Add noise to responses
                trait_responses = np.random.normal(trait_score, 0.5, questions_per_trait)
                trait_responses = np.clip(trait_responses, 1, 5)
                person_responses.extend(trait_responses)
            
            responses.append(person_responses)
            personality_scores.append(base_scores)
        
        X = np.array(responses)
        y = np.array(personality_scores)
        
        return X, y
    
    def train(self, X: Optional[np.ndarray] = None, y: Optional[np.ndarray] = None) -> Dict:
        """Train the personality assessment model."""
        try:
            if X is None or y is None:
                logger.info("No training data provided, generating synthetic data")
                X, y = self.prepare_training_data()
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train model for each personality trait
            self.model = {}
            metrics = {}
            
            for i, trait in enumerate(self.traits):
                # Train individual model for each trait
                rf_model = RandomForestRegressor(
                    n_estimators=100,
                    max_depth=10,
                    random_state=42,
                    n_jobs=-1
                )
                
                rf_model.fit(X_train_scaled, y_train[:, i])
                
                # Evaluate
                y_pred = rf_model.predict(X_test_scaled)
                mse = mean_squared_error(y_test[:, i], y_pred)
                r2 = r2_score(y_test[:, i], y_pred)
                
                self.model[trait] = rf_model
                metrics[trait] = {'mse': mse, 'r2': r2}
                
                logger.info(f"Trained {trait} model - MSE: {mse:.3f}, R2: {r2:.3f}")
            
            self.is_trained = True
            
            # Save model
            self.save_model()
            
            return {
                'status': 'success',
                'metrics': metrics,
                'training_samples': len(X_train),
                'test_samples': len(X_test)
            }
            
        except Exception as e:
            logger.error(f"Error training personality model: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def predict_personality(self, responses: List[float]) -> Dict[str, float]:
        """
        Predict personality traits from questionnaire responses.
        
        Args:
            responses: List of responses to personality questions (1-5 scale)
            
        Returns:
            Dictionary with personality trait scores
        """
        if not self.is_trained:
            if not self.load_model():
                raise ValueError("Model not trained and cannot be loaded")
        
        try:
            # Ensure we have the right number of responses
            expected_responses = len(self.traits) * 10  # 10 questions per trait
            if len(responses) != expected_responses:
                raise ValueError(f"Expected {expected_responses} responses, got {len(responses)}")
            
            # Scale input
            X = np.array(responses).reshape(1, -1)
            X_scaled = self.scaler.transform(X)
            
            # Predict each trait
            predictions = {}
            for trait in self.traits:
                score = self.model[trait].predict(X_scaled)[0]
                # Ensure score is within valid range
                score = max(1.0, min(5.0, score))
                predictions[trait] = round(score, 2)
            
            # Apply Vietnamese cultural adjustments
            predictions = self._apply_cultural_adjustments(predictions, responses)
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error predicting personality: {e}")
            raise
    
    def _apply_cultural_adjustments(self, predictions: Dict[str, float], responses: List[float]) -> Dict[str, float]:
        """Apply Vietnamese cultural adjustments to personality predictions."""
        adjusted = predictions.copy()
        
        # Vietnamese culture tends to emphasize:
        # - Higher conscientiousness (educational achievement, respect for authority)
        # - Higher agreeableness (group harmony, family importance)
        # - Moderate extraversion (balanced individual vs group focus)
        
        # These adjustments would be based on research and validation
        # For now, applying small cultural corrections
        
        # Slightly increase conscientiousness for Vietnamese context
        adjusted['conscientiousness'] = min(5.0, adjusted['conscientiousness'] * 1.05)
        
        # Slightly increase agreeableness for Vietnamese context
        adjusted['agreeableness'] = min(5.0, adjusted['agreeableness'] * 1.03)
        
        return adjusted
    
    def get_personality_insights(self, personality_scores: Dict[str, float]) -> Dict[str, str]:
        """Generate insights based on personality scores."""
        insights = {}
        
        for trait, score in personality_scores.items():
            if trait == 'openness':
                if score >= 4.0:
                    insights[trait] = "High creativity and openness to new experiences. Good fit for innovative fields."
                elif score >= 3.0:
                    insights[trait] = "Moderate openness. Balanced approach to new ideas and traditions."
                else:
                    insights[trait] = "Prefers familiar approaches. Good fit for structured, traditional roles."
            
            elif trait == 'conscientiousness':
                if score >= 4.0:
                    insights[trait] = "Highly organized and disciplined. Excellent for detail-oriented careers."
                elif score >= 3.0:
                    insights[trait] = "Good organizational skills. Reliable and responsible."
                else:
                    insights[trait] = "More flexible approach to structure. May prefer creative, less rigid roles."
            
            elif trait == 'extraversion':
                if score >= 4.0:
                    insights[trait] = "Highly social and energetic. Great for people-facing careers."
                elif score >= 3.0:
                    insights[trait] = "Balanced social energy. Can work well in teams or independently."
                else:
                    insights[trait] = "Prefers quieter environments. Good fit for independent, analytical work."
            
            elif trait == 'agreeableness':
                if score >= 4.0:
                    insights[trait] = "Highly cooperative and empathetic. Excellent for helping professions."
                elif score >= 3.0:
                    insights[trait] = "Good interpersonal skills. Works well in collaborative environments."
                else:
                    insights[trait] = "More competitive and direct. Good for leadership and competitive fields."
            
            elif trait == 'neuroticism':
                if score >= 4.0:
                    insights[trait] = "May experience higher stress. Benefits from supportive work environments."
                elif score >= 3.0:
                    insights[trait] = "Moderate stress response. Generally handles pressure well."
                else:
                    insights[trait] = "Very calm under pressure. Excellent for high-stress careers."
        
        return insights
    
    def get_career_compatibility(self, personality_scores: Dict[str, float]) -> Dict[str, float]:
        """
        Calculate compatibility scores for different career categories.
        
        Returns:
            Dictionary with career category compatibility scores (0-1)
        """
        compatibility = {}
        
        # Technology careers
        tech_score = (
            personality_scores['openness'] * 0.3 +
            personality_scores['conscientiousness'] * 0.3 +
            (6 - personality_scores['neuroticism']) * 0.2 +  # Lower neuroticism is better
            personality_scores['extraversion'] * 0.1 +
            personality_scores['agreeableness'] * 0.1
        ) / 5.0
        compatibility['Technology'] = round(tech_score, 2)
        
        # Business careers
        business_score = (
            personality_scores['extraversion'] * 0.3 +
            personality_scores['conscientiousness'] * 0.25 +
            personality_scores['openness'] * 0.2 +
            (6 - personality_scores['neuroticism']) * 0.15 +
            personality_scores['agreeableness'] * 0.1
        ) / 5.0
        compatibility['Business'] = round(business_score, 2)
        
        # Healthcare careers
        healthcare_score = (
            personality_scores['agreeableness'] * 0.35 +
            personality_scores['conscientiousness'] * 0.25 +
            (6 - personality_scores['neuroticism']) * 0.2 +
            personality_scores['openness'] * 0.1 +
            personality_scores['extraversion'] * 0.1
        ) / 5.0
        compatibility['Healthcare'] = round(healthcare_score, 2)
        
        # Education careers
        education_score = (
            personality_scores['agreeableness'] * 0.3 +
            personality_scores['extraversion'] * 0.25 +
            personality_scores['conscientiousness'] * 0.2 +
            personality_scores['openness'] * 0.15 +
            (6 - personality_scores['neuroticism']) * 0.1
        ) / 5.0
        compatibility['Education'] = round(education_score, 2)
        
        # Creative careers
        creative_score = (
            personality_scores['openness'] * 0.4 +
            personality_scores['extraversion'] * 0.2 +
            personality_scores['agreeableness'] * 0.15 +
            personality_scores['conscientiousness'] * 0.15 +
            (6 - personality_scores['neuroticism']) * 0.1
        ) / 5.0
        compatibility['Creative'] = round(creative_score, 2)
        
        return compatibility

# Initialize global model instance
personality_model = PersonalityModel()

# Auto-load model if available, otherwise train
if not personality_model.load_model():
    logger.info("Training new personality model...")
    personality_model.train()
