from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime

class CareerBase(BaseModel):
    name_en: str
    name_vi: str
    description_en: Optional[str] = None
    description_vi: Optional[str] = None
    category: str
    subcategory: Optional[str] = None
    # Job market data for Vietnam
    average_salary_vnd: Optional[int] = None
    salary_range_min_vnd: Optional[int] = None
    salary_range_max_vnd: Optional[int] = None
    employment_growth_rate: Optional[float] = None
    job_openings_count: Optional[int] = None
    # Education requirements
    education_level_required: Optional[str] = None
    typical_education_path: Optional[str] = None
    # Work environment
    work_environment: Optional[str] = None
    work_schedule: Optional[str] = None
    work_setting: Optional[str] = None
    # Career pathways
    advancement_opportunities: Optional[str] = None
    # Vietnamese specific data
    demand_in_vietnam: Optional[str] = None

class CareerCreate(CareerBase):
    required_skills: Optional[List[str]] = None
    related_careers: Optional[List[str]] = None
    regions_with_high_demand: Optional[List[str]] = None

class CareerUpdate(CareerBase):
    name_en: Optional[str] = None
    name_vi: Optional[str] = None
    category: Optional[str] = None
    required_skills: Optional[List[str]] = None
    related_careers: Optional[List[str]] = None
    regions_with_high_demand: Optional[List[str]] = None

class CareerInDBBase(CareerBase):
    id: int
    required_skills: Optional[List[str]] = None
    related_careers: Optional[List[str]] = None
    regions_with_high_demand: Optional[List[str]] = None
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class Career(CareerInDBBase):
    pass

class CareerInterestBase(BaseModel):
    interest_category: str
    interest_score: float

class CareerInterestCreate(CareerInterestBase):
    career_id: int

class CareerInterestUpdate(CareerInterestBase):
    pass

class CareerInterestInDBBase(CareerInterestBase):
    id: int
    career_id: int
    created_at: datetime

    class Config:
        orm_mode = True

class CareerInterest(CareerInterestInDBBase):
    pass

class CareerSearch(BaseModel):
    query: Optional[str] = None
    category: Optional[str] = None
    min_salary: Optional[int] = None
    max_salary: Optional[int] = None
    demand_in_vietnam: Optional[str] = None
    education_level_required: Optional[str] = None

class CareerFilter(BaseModel):
    categories: Optional[List[str]] = None
    salary_range: Optional[str] = None  # low, medium, high
    demand_level: Optional[str] = None  # high, medium, low
    education_levels: Optional[List[str]] = None

class CareerDetail(Career):
    personality_match_openness: Optional[float] = None
    personality_match_conscientiousness: Optional[float] = None
    personality_match_extraversion: Optional[float] = None
    personality_match_agreeableness: Optional[float] = None
    personality_match_neuroticism: Optional[float] = None
    career_interests: Optional[List[CareerInterest]] = None