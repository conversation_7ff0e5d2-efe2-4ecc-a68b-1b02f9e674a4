from sqlalchemy import Column, Integer, String, DateTime, Float, Text, Boolean, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database.connection import Base
from enum import Enum as PyEnum
from typing import Optional

class PaymentStatus(str, PyEnum):
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"

class PaymentMethod(str, PyEnum):
    MOMO = "momo"
    ZALOPAY = "zalopay"
    VIETTELPAY = "viettelpay"
    BANK_TRANSFER = "bank_transfer"
    CASH = "cash"

class SubscriptionTier(str, PyEnum):
    FREE = "free"
    BASIC = "basic"
    PREMIUM = "premium"

class Payment(Base):
    __tablename__ = "payments"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    # Payment details
    amount = Column(Float, nullable=False)  # Amount in VND
    currency = Column(String, default="VND")
    payment_method = Column(Enum(PaymentMethod), nullable=False)
    payment_status = Column(Enum(PaymentStatus), default=PaymentStatus.PENDING)
    transaction_id = Column(String, unique=True, index=True, nullable=True)  # External transaction ID
    # Subscription details
    subscription_tier = Column(Enum(SubscriptionTier), nullable=False)
    subscription_start_date = Column(DateTime, nullable=True)
    subscription_end_date = Column(DateTime, nullable=True)
    # Vietnamese payment providers specific fields
    momo_request_id = Column(String, nullable=True)
    zalopay_app_trans_id = Column(String, nullable=True)
    viettelpay_transaction_id = Column(String, nullable=True)
    bank_transfer_reference = Column(String, nullable=True)
    # Metadata
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    completed_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="payments")

class Subscription(Base):
    __tablename__ = "subscriptions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    tier = Column(Enum(SubscriptionTier), nullable=False)
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    is_active = Column(Boolean, default=True)
    # Payment reference
    payment_id = Column(Integer, ForeignKey("payments.id"), nullable=True)
    # Features
    max_recommendations = Column(Integer, nullable=False, default=5)  # Max recommendations per month
    can_export_reports = Column(Boolean, default=False)  # Can export career reports
    priority_support = Column(Boolean, default=False)  # Priority customer support
    # Metadata
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User")
    payment = relationship("Payment")