from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime

class PersonalityAssessmentBase(BaseModel):
    # Big 5 personality traits (adapted for Vietnamese culture)
    openness: float
    conscientiousness: float
    extraversion: float
    agreeableness: float
    neuroticism: float
    # Additional Vietnamese cultural traits
    collectivism: Optional[float] = None
    familism: Optional[float] = None
    respect_authority: Optional[float] = None

class PersonalityAssessmentCreate(PersonalityAssessmentBase):
    responses: Optional[Dict[str, Any]] = None

class PersonalityAssessmentUpdate(PersonalityAssessmentBase):
    pass

class PersonalityAssessmentInDBBase(PersonalityAssessmentBase):
    id: int
    user_id: int
    responses: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class PersonalityAssessment(PersonalityAssessmentInDBBase):
    pass

class SkillsAssessmentBase(BaseModel):
    # Academic subjects relevant to Vietnamese education system
    mathematics: Optional[float] = None
    literature: Optional[float] = None
    english: Optional[float] = None
    physics: Optional[float] = None
    chemistry: Optional[float] = None
    biology: Optional[float] = None
    history: Optional[float] = None
    geography: Optional[float] = None
    civic_education: Optional[float] = None
    technology: Optional[float] = None
    informatics: Optional[float] = None
    # Additional skills
    critical_thinking: Optional[float] = None
    problem_solving: Optional[float] = None
    communication: Optional[float] = None
    creativity: Optional[float] = None
    leadership: Optional[float] = None

class SkillsAssessmentCreate(SkillsAssessmentBase):
    academic_performance: Optional[Dict[str, Any]] = None

class SkillsAssessmentUpdate(SkillsAssessmentBase):
    pass

class SkillsAssessmentInDBBase(SkillsAssessmentBase):
    id: int
    user_id: int
    academic_performance: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class SkillsAssessment(SkillsAssessmentInDBBase):
    pass

class InterestAssessmentBase(BaseModel):
    # Interest categories with RIASEC model
    realistic: Optional[float] = None
    investigative: Optional[float] = None
    artistic: Optional[float] = None
    social: Optional[float] = None
    enterprising: Optional[float] = None
    conventional: Optional[float] = None
    # Vietnamese specific interests
    agriculture: Optional[float] = None
    manufacturing: Optional[float] = None
    technology_it: Optional[float] = None
    business_finance: Optional[float] = None
    education: Optional[float] = None
    healthcare: Optional[float] = None
    media_communication: Optional[float] = None
    public_service: Optional[float] = None
    tourism: Optional[float] = None

class InterestAssessmentCreate(InterestAssessmentBase):
    interest_responses: Optional[Dict[str, Any]] = None

class InterestAssessmentUpdate(InterestAssessmentBase):
    pass

class InterestAssessmentInDBBase(InterestAssessmentBase):
    id: int
    user_id: int
    interest_responses: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class InterestAssessment(InterestAssessmentInDBBase):
    pass

class AssessmentQuestion(BaseModel):
    id: int
    category: str
    question_en: str
    question_vi: str
    question_type: str  # text, multiple_choice, rating_scale
    options: Optional[Dict[str, Any]] = None

class AssessmentResponse(BaseModel):
    question_id: int
    response: Any