from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.database.connection import get_db
from app.schemas.assessment import (
    PersonalityAssessmentCreate, PersonalityAssessment, 
    SkillsAssessmentCreate, SkillsAssessment,
    InterestAssessmentCreate, InterestAssessment,
    AssessmentQuestion, AssessmentResponse
)
from app.models.assessment import PersonalityAssessment as PersonalityAssessmentModel
from app.models.assessment import SkillsAssessment as SkillsAssessmentModel
from app.models.assessment import InterestAssessment as InterestAssessmentModel
from datetime import datetime
import json

router = APIRouter()

# Personality Assessment Routes
@router.post("/personality/", response_model=PersonalityAssessment)
def create_personality_assessment(
    assessment: PersonalityAssessmentCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Create a new personality assessment for the current user.
    """
    db_assessment = PersonalityAssessmentModel(
        user_id=current_user.id,
        openness=assessment.openness,
        conscientiousness=assessment.conscientiousness,
        extraversion=assessment.extraversion,
        agreeableness=assessment.agreeableness,
        neuroticism=assessment.neuroticism,
        collectivism=assessment.collectivism,
        familism=assessment.familism,
        respect_authority=assessment.respect_authority,
        responses=assessment.responses
    )
    
    db.add(db_assessment)
    db.commit()
    db.refresh(db_assessment)
    
    return db_assessment

@router.get("/personality/latest/", response_model=PersonalityAssessment)
def get_latest_personality_assessment(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get the latest personality assessment for the current user.
    """
    db_assessment = db.query(PersonalityAssessmentModel)\
        .filter(PersonalityAssessmentModel.user_id == current_user.id)\
        .order_by(PersonalityAssessmentModel.created_at.desc())\
        .first()
    
    if db_assessment is None:
        raise HTTPException(status_code=404, detail="No personality assessment found")
    
    return db_assessment

@router.get("/personality/{assessment_id}/", response_model=PersonalityAssessment)
def get_personality_assessment(
    assessment_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get a specific personality assessment by ID.
    """
    db_assessment = db.query(PersonalityAssessmentModel)\
        .filter(PersonalityAssessmentModel.id == assessment_id, 
                PersonalityAssessmentModel.user_id == current_user.id)\
        .first()
    
    if db_assessment is None:
        raise HTTPException(status_code=404, detail="Personality assessment not found")
    
    return db_assessment

# Skills Assessment Routes
@router.post("/skills/", response_model=SkillsAssessment)
def create_skills_assessment(
    assessment: SkillsAssessmentCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Create a new skills assessment for the current user.
    """
    db_assessment = SkillsAssessmentModel(
        user_id=current_user.id,
        mathematics=assessment.mathematics,
        literature=assessment.literature,
        english=assessment.english,
        physics=assessment.physics,
        chemistry=assessment.chemistry,
        biology=assessment.biology,
        history=assessment.history,
        geography=assessment.geography,
        civic_education=assessment.civic_education,
        technology=assessment.technology,
        informatics=assessment.informatics,
        critical_thinking=assessment.critical_thinking,
        problem_solving=assessment.problem_solving,
        communication=assessment.communication,
        creativity=assessment.creativity,
        leadership=assessment.leadership,
        academic_performance=assessment.academic_performance
    )
    
    db.add(db_assessment)
    db.commit()
    db.refresh(db_assessment)
    
    return db_assessment

@router.get("/skills/latest/", response_model=SkillsAssessment)
def get_latest_skills_assessment(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get the latest skills assessment for the current user.
    """
    db_assessment = db.query(SkillsAssessmentModel)\
        .filter(SkillsAssessmentModel.user_id == current_user.id)\
        .order_by(SkillsAssessmentModel.created_at.desc())\
        .first()
    
    if db_assessment is None:
        raise HTTPException(status_code=404, detail="No skills assessment found")
    
    return db_assessment

# Interest Assessment Routes
@router.post("/interest/", response_model=InterestAssessment)
def create_interest_assessment(
    assessment: InterestAssessmentCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Create a new interest assessment for the current user.
    """
    db_assessment = InterestAssessmentModel(
        user_id=current_user.id,
        realistic=assessment.realistic,
        investigative=assessment.investigative,
        artistic=assessment.artistic,
        social=assessment.social,
        enterprising=assessment.enterprising,
        conventional=assessment.conventional,
        agriculture=assessment.agriculture,
        manufacturing=assessment.manufacturing,
        technology_it=assessment.technology_it,
        business_finance=assessment.business_finance,
        education=assessment.education,
        healthcare=assessment.healthcare,
        media_communication=assessment.media_communication,
        public_service=assessment.public_service,
        tourism=assessment.tourism,
        interest_responses=assessment.interest_responses
    )
    
    db.add(db_assessment)
    db.commit()
    db.refresh(db_assessment)
    
    return db_assessment

@router.get("/interest/latest/", response_model=InterestAssessment)
def get_latest_interest_assessment(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get the latest interest assessment for the current user.
    """
    db_assessment = db.query(InterestAssessmentModel)\
        .filter(InterestAssessmentModel.user_id == current_user.id)\
        .order_by(InterestAssessmentModel.created_at.desc())\
        .first()
    
    if db_assessment is None:
        raise HTTPException(status_code=404, detail="No interest assessment found")
    
    return db_assessment

# Assessment Questions Routes
@router.get("/questions/personality/", response_model=List[AssessmentQuestion])
def get_personality_questions(
    db: Session = Depends(get_db)
):
    """
    Get all personality assessment questions.
    """
    # In a real application, these would come from the database
    # This is a simplified example
    questions = [
        AssessmentQuestion(
            id=1,
            category="openness",
            question_en="I enjoy trying new things.",
            question_vi="Tôi thích thử những điều mới.",
            question_type="rating_scale",
            options={"min": 1, "max": 5, "labels": ["Strongly Disagree", "Disagree", "Neutral", "Agree", "Strongly Agree"]}
        ),
        AssessmentQuestion(
            id=2,
            category="conscientiousness",
            question_en="I am organized and plan ahead.",
            question_vi="Tôi có tổ chức và lên kế hoạch trước.",
            question_type="rating_scale",
            options={"min": 1, "max": 5, "labels": ["Strongly Disagree", "Disagree", "Neutral", "Agree", "Strongly Agree"]}
        ),
        # Add more questions as needed
    ]
    
    return questions

@router.get("/questions/skills/", response_model=List[AssessmentQuestion])
def get_skills_questions(
    db: Session = Depends(get_db)
):
    """
    Get all skills assessment questions.
    """
    # In a real application, these would come from the database
    questions = [
        AssessmentQuestion(
            id=101,
            category="academic",
            question_en="What is your average grade in Mathematics?",
            question_vi="Điểm trung bình môn Toán của bạn là bao nhiêu?",
            question_type="number"
        ),
        AssessmentQuestion(
            id=102,
            category="skills",
            question_en="Rate your problem-solving ability.",
            question_vi="Đánh giá khả năng giải quyết vấn đề của bạn.",
            question_type="rating_scale",
            options={"min": 1, "max": 10}
        ),
        # Add more questions as needed
    ]
    
    return questions

@router.get("/questions/interest/", response_model=List[AssessmentQuestion])
def get_interest_questions(
    db: Session = Depends(get_db)
):
    """
    Get all interest assessment questions.
    """
    # In a real application, these would come from the database
    questions = [
        AssessmentQuestion(
            id=201,
            category="interests",
            question_en="How interested are you in working with technology?",
            question_vi="Bạn quan tâm đến việc làm việc với công nghệ như thế nào?",
            question_type="rating_scale",
            options={"min": 1, "max": 5, "labels": ["Not Interested", "Slightly Interested", "Moderately Interested", "Very Interested", "Extremely Interested"]}
        ),
        AssessmentQuestion(
            id=202,
            category="interests",
            question_en="How interested are you in helping people?",
            question_vi="Bạn quan tâm đến việc giúp đỡ người khác như thế nào?",
            question_type="rating_scale",
