from typing import Any, Dict, Optional
from fastapi import HTTPException, status


class CustomException(Exception):
    """Base custom exception class for the application."""
    
    def __init__(
        self,
        message: str,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(CustomException):
    """Exception raised for validation errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details
        )


class AuthenticationError(CustomException):
    """Exception raised for authentication errors."""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class AuthorizationError(CustomException):
    """Exception raised for authorization errors."""
    
    def __init__(self, message: str = "Access denied"):
        super().__init__(
            message=message,
            status_code=status.HTTP_403_FORBIDDEN
        )


class NotFoundError(CustomException):
    """Exception raised when a resource is not found."""
    
    def __init__(self, resource: str, identifier: Any = None):
        message = f"{resource} not found"
        if identifier:
            message += f" (ID: {identifier})"
        
        super().__init__(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            details={"resource": resource, "identifier": identifier}
        )


class ConflictError(CustomException):
    """Exception raised for resource conflicts."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_409_CONFLICT,
            details=details
        )


class RateLimitError(CustomException):
    """Exception raised when rate limit is exceeded."""
    
    def __init__(self, message: str = "Rate limit exceeded", retry_after: int = None):
        details = {}
        if retry_after:
            details["retry_after"] = retry_after
            
        super().__init__(
            message=message,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            details=details
        )


class PaymentError(CustomException):
    """Exception raised for payment-related errors."""
    
    def __init__(self, message: str, payment_id: str = None):
        details = {}
        if payment_id:
            details["payment_id"] = payment_id
            
        super().__init__(
            message=message,
            status_code=status.HTTP_402_PAYMENT_REQUIRED,
            details=details
        )


class MLModelError(CustomException):
    """Exception raised for machine learning model errors."""
    
    def __init__(self, message: str, model_name: str = None):
        details = {}
        if model_name:
            details["model_name"] = model_name
            
        super().__init__(
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details
        )


class DatabaseError(CustomException):
    """Exception raised for database-related errors."""
    
    def __init__(self, message: str, operation: str = None):
        details = {}
        if operation:
            details["operation"] = operation
            
        super().__init__(
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details
        )


class ExternalServiceError(CustomException):
    """Exception raised for external service errors."""
    
    def __init__(self, message: str, service_name: str = None, status_code: int = None):
        details = {}
        if service_name:
            details["service_name"] = service_name
        if status_code:
            details["external_status_code"] = status_code
            
        super().__init__(
            message=message,
            status_code=status.HTTP_502_BAD_GATEWAY,
            details=details
        )


class FileUploadError(CustomException):
    """Exception raised for file upload errors."""
    
    def __init__(self, message: str, filename: str = None, file_size: int = None):
        details = {}
        if filename:
            details["filename"] = filename
        if file_size:
            details["file_size"] = file_size
            
        super().__init__(
            message=message,
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            details=details
        )


# Assessment-specific exceptions
class AssessmentError(CustomException):
    """Exception raised for assessment-related errors."""
    
    def __init__(self, message: str, assessment_type: str = None):
        details = {}
        if assessment_type:
            details["assessment_type"] = assessment_type
            
        super().__init__(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            details=details
        )


class RecommendationError(CustomException):
    """Exception raised for recommendation generation errors."""
    
    def __init__(self, message: str, user_id: int = None):
        details = {}
        if user_id:
            details["user_id"] = user_id
            
        super().__init__(
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details
        )


# Career-specific exceptions
class CareerNotFoundError(NotFoundError):
    """Exception raised when a career is not found."""
    
    def __init__(self, career_id: int = None, career_name: str = None):
        identifier = career_id or career_name
        super().__init__(resource="Career", identifier=identifier)


class UserNotFoundError(NotFoundError):
    """Exception raised when a user is not found."""
    
    def __init__(self, user_id: int = None, username: str = None, email: str = None):
        identifier = user_id or username or email
        super().__init__(resource="User", identifier=identifier)


class AssessmentNotFoundError(NotFoundError):
    """Exception raised when an assessment is not found."""
    
    def __init__(self, assessment_id: int = None, assessment_type: str = None):
        identifier = assessment_id or assessment_type
        super().__init__(resource="Assessment", identifier=identifier)


class PaymentNotFoundError(NotFoundError):
    """Exception raised when a payment is not found."""
    
    def __init__(self, payment_id: int = None, transaction_id: str = None):
        identifier = payment_id or transaction_id
        super().__init__(resource="Payment", identifier=identifier)


# Utility functions for common error scenarios
def raise_not_found(resource: str, identifier: Any = None) -> None:
    """Raise a NotFoundError with the given resource and identifier."""
    raise NotFoundError(resource=resource, identifier=identifier)


def raise_validation_error(message: str, field: str = None, value: Any = None) -> None:
    """Raise a ValidationError with field details."""
    details = {}
    if field:
        details["field"] = field
    if value is not None:
        details["value"] = value
    
    raise ValidationError(message=message, details=details)


def raise_conflict_error(resource: str, field: str = None, value: Any = None) -> None:
    """Raise a ConflictError for resource conflicts."""
    message = f"{resource} already exists"
    details = {"resource": resource}
    
    if field and value:
        message += f" with {field}: {value}"
        details.update({"field": field, "value": value})
    
    raise ConflictError(message=message, details=details)


def raise_unauthorized(message: str = "Authentication required") -> None:
    """Raise an AuthenticationError."""
    raise AuthenticationError(message=message)


def raise_forbidden(message: str = "Access denied") -> None:
    """Raise an AuthorizationError."""
    raise AuthorizationError(message=message)
