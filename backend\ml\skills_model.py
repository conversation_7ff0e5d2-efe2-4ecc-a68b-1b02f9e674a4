import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, mean_squared_error
import joblib
import logging
from typing import Dict, List, Tuple, Optional
import os

logger = logging.getLogger(__name__)

class SkillsModel:
    """
    Machine learning model for skills assessment and career matching.
    Evaluates technical and soft skills relevant to Vietnamese job market.
    """
    
    def __init__(self, model_path: str = "models/skills_model.pkl"):
        self.model_path = model_path
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False
        
        # Core skill categories for Vietnamese job market
        self.skill_categories = {
            'technical': [
                'programming',
                'data_analysis',
                'digital_marketing',
                'design',
                'engineering',
                'accounting',
                'foreign_languages'
            ],
            'soft_skills': [
                'communication',
                'leadership',
                'teamwork',
                'problem_solving',
                'time_management',
                'adaptability',
                'critical_thinking'
            ],
            'vietnamese_specific': [
                'vietnamese_writing',
                'english_proficiency',
                'cultural_awareness',
                'government_relations',
                'local_market_knowledge'
            ]
        }
        
        # All skills combined
        self.all_skills = []
        for category in self.skill_categories.values():
            self.all_skills.extend(category)
    
    def load_model(self) -> bool:
        """Load pre-trained model from disk."""
        try:
            if os.path.exists(self.model_path):
                model_data = joblib.load(self.model_path)
                self.model = model_data['model']
                self.scaler = model_data['scaler']
                self.is_trained = True
                logger.info("Skills model loaded successfully")
                return True
            else:
                logger.warning(f"Model file not found: {self.model_path}")
                return False
        except Exception as e:
            logger.error(f"Error loading skills model: {e}")
            return False
    
    def save_model(self) -> bool:
        """Save trained model to disk."""
        try:
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'skills': self.all_skills,
                'skill_categories': self.skill_categories
            }
            joblib.save(model_data, self.model_path)
            logger.info(f"Skills model saved to {self.model_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving skills model: {e}")
            return False
    
    def prepare_training_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare synthetic training data for skills assessment.
        In production, this would use real assessment and career outcome data.
        """
        np.random.seed(42)
        n_samples = 1500
        
        # Generate synthetic skill assessment data
        skill_assessments = []
        career_outcomes = []
        
        # Define career-skill relationships
        career_skill_weights = {
            'Software Engineer': {
                'programming': 0.9, 'problem_solving': 0.8, 'critical_thinking': 0.7,
                'english_proficiency': 0.6, 'teamwork': 0.6
            },
            'Data Scientist': {
                'data_analysis': 0.9, 'programming': 0.8, 'critical_thinking': 0.8,
                'problem_solving': 0.7, 'english_proficiency': 0.7
            },
            'Marketing Manager': {
                'digital_marketing': 0.8, 'communication': 0.9, 'leadership': 0.7,
                'vietnamese_writing': 0.8, 'cultural_awareness': 0.7
            },
            'Business Analyst': {
                'data_analysis': 0.7, 'problem_solving': 0.8, 'communication': 0.7,
                'critical_thinking': 0.8, 'english_proficiency': 0.6
            },
            'Teacher': {
                'communication': 0.9, 'vietnamese_writing': 0.8, 'leadership': 0.6,
                'adaptability': 0.7, 'cultural_awareness': 0.8
            }
        }
        
        careers = list(career_skill_weights.keys())
        
        for i in range(n_samples):
            # Randomly select a career
            career = np.random.choice(careers)
            career_weights = career_skill_weights[career]
            
            # Generate skill scores based on career requirements
            skill_scores = {}
            for skill in self.all_skills:
                if skill in career_weights:
                    # Higher probability of good scores for relevant skills
                    base_score = np.random.normal(career_weights[skill] * 5, 0.8)
                else:
                    # Random scores for non-relevant skills
                    base_score = np.random.normal(2.5, 1.0)
                
                skill_scores[skill] = max(1, min(5, base_score))
            
            skill_assessments.append(list(skill_scores.values()))
            career_outcomes.append(career)
        
        X = np.array(skill_assessments)
        y = np.array(career_outcomes)
        
        return X, y
    
    def train(self, X: Optional[np.ndarray] = None, y: Optional[np.ndarray] = None) -> Dict:
        """Train the skills assessment model."""
        try:
            if X is None or y is None:
                logger.info("No training data provided, generating synthetic data")
                X, y = self.prepare_training_data()
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train career prediction model
            self.model = RandomForestClassifier(
                n_estimators=100,
                max_depth=15,
                random_state=42,
                n_jobs=-1
            )
            
            self.model.fit(X_train_scaled, y_train)
            
            # Evaluate
            y_pred = self.model.predict(X_test_scaled)
            accuracy = accuracy_score(y_test, y_pred)
            
            self.is_trained = True
            
            # Save model
            self.save_model()
            
            logger.info(f"Skills model trained - Accuracy: {accuracy:.3f}")
            
            return {
                'status': 'success',
                'accuracy': accuracy,
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'feature_importance': dict(zip(self.all_skills, self.model.feature_importances_))
            }
            
        except Exception as e:
            logger.error(f"Error training skills model: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def assess_skills(self, skill_responses: Dict[str, float]) -> Dict:
        """
        Assess skills and predict career compatibility.
        
        Args:
            skill_responses: Dictionary with skill names and scores (1-5)
            
        Returns:
            Dictionary with skill assessment results
        """
        if not self.is_trained:
            if not self.load_model():
                raise ValueError("Model not trained and cannot be loaded")
        
        try:
            # Prepare input vector
            skill_vector = []
            for skill in self.all_skills:
                score = skill_responses.get(skill, 2.5)  # Default neutral score
                skill_vector.append(score)
            
            X = np.array(skill_vector).reshape(1, -1)
            X_scaled = self.scaler.transform(X)
            
            # Predict career probabilities
            career_probabilities = self.model.predict_proba(X_scaled)[0]
            career_classes = self.model.classes_
            
            # Create career compatibility scores
            career_compatibility = {}
            for i, career in enumerate(career_classes):
                career_compatibility[career] = round(career_probabilities[i], 3)
            
            # Sort by compatibility
            sorted_careers = sorted(career_compatibility.items(), key=lambda x: x[1], reverse=True)
            
            # Analyze skill strengths and weaknesses
            skill_analysis = self._analyze_skills(skill_responses)
            
            # Generate recommendations
            recommendations = self._generate_skill_recommendations(skill_responses, sorted_careers[:3])
            
            return {
                'career_compatibility': dict(sorted_careers),
                'top_careers': [career for career, score in sorted_careers[:5]],
                'skill_analysis': skill_analysis,
                'recommendations': recommendations,
                'overall_skill_level': self._calculate_overall_skill_level(skill_responses)
            }
            
        except Exception as e:
            logger.error(f"Error assessing skills: {e}")
            raise
    
    def _analyze_skills(self, skill_responses: Dict[str, float]) -> Dict:
        """Analyze skill strengths and areas for improvement."""
        analysis = {
            'strengths': [],
            'areas_for_improvement': [],
            'category_scores': {}
        }
        
        # Calculate category averages
        for category, skills in self.skill_categories.items():
            category_scores = [skill_responses.get(skill, 2.5) for skill in skills]
            avg_score = sum(category_scores) / len(category_scores)
            analysis['category_scores'][category] = round(avg_score, 2)
        
        # Identify strengths (scores >= 4.0)
        for skill, score in skill_responses.items():
            if score >= 4.0:
                analysis['strengths'].append({
                    'skill': skill,
                    'score': score,
                    'level': 'Strong' if score >= 4.5 else 'Good'
                })
        
        # Identify areas for improvement (scores < 3.0)
        for skill, score in skill_responses.items():
            if score < 3.0:
                analysis['areas_for_improvement'].append({
                    'skill': skill,
                    'score': score,
                    'priority': 'High' if score < 2.0 else 'Medium'
                })
        
        return analysis
    
    def _generate_skill_recommendations(self, skill_responses: Dict[str, float], top_careers: List[Tuple[str, float]]) -> List[Dict]:
        """Generate personalized skill development recommendations."""
        recommendations = []
        
        # Career-specific skill requirements
        career_requirements = {
            'Software Engineer': ['programming', 'problem_solving', 'english_proficiency'],
            'Data Scientist': ['data_analysis', 'programming', 'critical_thinking'],
            'Marketing Manager': ['digital_marketing', 'communication', 'vietnamese_writing'],
            'Business Analyst': ['data_analysis', 'problem_solving', 'communication'],
            'Teacher': ['communication', 'vietnamese_writing', 'leadership']
        }
        
        for career, compatibility in top_careers:
            if career in career_requirements:
                required_skills = career_requirements[career]
                skill_gaps = []
                
                for skill in required_skills:
                    current_score = skill_responses.get(skill, 2.5)
                    if current_score < 3.5:  # Below good level
                        skill_gaps.append({
                            'skill': skill,
                            'current_score': current_score,
                            'target_score': 4.0,
                            'gap': 4.0 - current_score
                        })
                
                if skill_gaps:
                    recommendations.append({
                        'career': career,
                        'compatibility': compatibility,
                        'skill_gaps': skill_gaps,
                        'priority': 'High' if compatibility > 0.3 else 'Medium'
                    })
        
        return recommendations
    
    def _calculate_overall_skill_level(self, skill_responses: Dict[str, float]) -> Dict:
        """Calculate overall skill level assessment."""
        scores = list(skill_responses.values())
        avg_score = sum(scores) / len(scores)
        
        # Determine skill level
        if avg_score >= 4.0:
            level = "Advanced"
            description = "Strong overall skill set with expertise in multiple areas"
        elif avg_score >= 3.0:
            level = "Intermediate"
            description = "Good foundation with room for growth in specific areas"
        elif avg_score >= 2.0:
            level = "Beginner"
            description = "Developing skill set with significant growth opportunities"
        else:
            level = "Entry Level"
            description = "Early stage of skill development with substantial learning needed"
        
        return {
            'level': level,
            'average_score': round(avg_score, 2),
            'description': description,
            'percentile': self._calculate_percentile(avg_score)
        }
    
    def _calculate_percentile(self, score: float) -> int:
        """Calculate approximate percentile based on score."""
        # Simple percentile calculation - would be more accurate with real data
        if score >= 4.5:
            return 90
        elif score >= 4.0:
            return 75
        elif score >= 3.5:
            return 60
        elif score >= 3.0:
            return 40
        elif score >= 2.5:
            return 25
        else:
            return 10
    
    def get_skill_development_path(self, current_skills: Dict[str, float], target_career: str) -> Dict:
        """Generate a skill development path for a target career."""
        # Career skill requirements (would be more detailed in production)
        career_skills = {
            'Software Engineer': {
                'programming': 4.5, 'problem_solving': 4.0, 'critical_thinking': 4.0,
                'english_proficiency': 3.5, 'teamwork': 3.5
            },
            'Data Scientist': {
                'data_analysis': 4.5, 'programming': 4.0, 'critical_thinking': 4.5,
                'problem_solving': 4.0, 'english_proficiency': 4.0
            },
            'Marketing Manager': {
                'digital_marketing': 4.0, 'communication': 4.5, 'leadership': 4.0,
                'vietnamese_writing': 4.0, 'cultural_awareness': 3.5
            }
        }
        
        if target_career not in career_skills:
            return {'error': f'Career {target_career} not found in database'}
        
        required_skills = career_skills[target_career]
        development_plan = []
        
        for skill, target_level in required_skills.items():
            current_level = current_skills.get(skill, 2.0)
            if current_level < target_level:
                gap = target_level - current_level
                development_plan.append({
                    'skill': skill,
                    'current_level': current_level,
                    'target_level': target_level,
                    'gap': round(gap, 1),
                    'priority': 'High' if gap > 1.5 else 'Medium',
                    'estimated_time_months': max(1, int(gap * 3))  # 3 months per skill point
                })
        
        # Sort by priority and gap size
        development_plan.sort(key=lambda x: (x['priority'] == 'High', x['gap']), reverse=True)
        
        return {
            'target_career': target_career,
            'development_plan': development_plan,
            'total_estimated_time_months': sum(item['estimated_time_months'] for item in development_plan),
            'readiness_score': self._calculate_readiness_score(current_skills, required_skills)
        }
    
    def _calculate_readiness_score(self, current_skills: Dict[str, float], required_skills: Dict[str, float]) -> float:
        """Calculate how ready someone is for a career based on current skills."""
        total_score = 0
        total_weight = 0
        
        for skill, required_level in required_skills.items():
            current_level = current_skills.get(skill, 2.0)
            score = min(1.0, current_level / required_level)
            total_score += score
            total_weight += 1
        
        return round(total_score / total_weight if total_weight > 0 else 0, 2)

# Initialize global model instance
skills_model = SkillsModel()

# Auto-load model if available, otherwise train
if not skills_model.load_model():
    logger.info("Training new skills model...")
    skills_model.train()
