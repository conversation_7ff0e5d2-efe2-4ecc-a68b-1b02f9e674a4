from sqlalchemy import Column, Integer, String, DateTime, Float, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database.connection import Base
from typing import Dict, Any

class PersonalityAssessment(Base):
    __tablename__ = "personality_assessments"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    # Big 5 personality traits (adapted for Vietnamese culture)
    openness = Column(Float, nullable=False)  # Tính cởi mở
    conscientiousness = Column(Float, nullable=False)  # Tính chăm chỉ, kỷ luật
    extraversion = Column(Float, nullable=False)  # Tính hướng ngoại
    agreeableness = Column(Float, nullable=False)  # T<PERSON>h thân thiện, hòa nhã
    neuroticism = Column(Float, nullable=False)  # <PERSON><PERSON><PERSON> <PERSON><PERSON> lo <PERSON>, nh<PERSON><PERSON> cảm
    # Additional Vietnamese cultural traits
    collectivism = Column(Float, nullable=True)  # Tính cộng đồng
    familism = Column(Float, nullable=True)  # Tính gia đình
    respect_authority = Column(Float, nullable=True)  # Tính tôn trọng quyền lực
    responses = Column(JSON, nullable=True)  # Raw responses to questions
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relationship
    user = relationship("User", back_populates="personality_assessments")

class SkillsAssessment(Base):
    __tablename__ = "skills_assessments"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    # Academic subjects relevant to Vietnamese education system
    mathematics = Column(Float, nullable=True)
    literature = Column(Float, nullable=True)
    english = Column(Float, nullable=True)
    physics = Column(Float, nullable=True)
    chemistry = Column(Float, nullable=True)
    biology = Column(Float, nullable=True)
    history = Column(Float, nullable=True)
    geography = Column(Float, nullable=True)
    civic_education = Column(Float, nullable=True)  # Giáo dục công dân
    technology = Column(Float, nullable=True)  # Công nghệ
    informatics = Column(Float, nullable=True)  # Tin học
    # Additional skills
    critical_thinking = Column(Float, nullable=True)
    problem_solving = Column(Float, nullable=True)
    communication = Column(Float, nullable=True)
    creativity = Column(Float, nullable=True)
    leadership = Column(Float, nullable=True)
    academic_performance = Column(JSON, nullable=True)  # Raw academic data
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relationship
    user = relationship("User", back_populates="skills_assessments")

class InterestAssessment(Base):
    __tablename__ = "interest_assessments"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    # Interest categories with 200+ career options
    realistic = Column(Float, nullable=True)  # Thực tế
    investigative = Column(Float, nullable=True)  # Điều tra, nghiên cứu
    artistic = Column(Float, nullable=True)  # Nghệ thuật
    social = Column(Float, nullable=True)  #Xã hội
    enterprising = Column(Float, nullable=True)  # Kinh doanh
    conventional = Column(Float, nullable=True)  # Truyền thống
    # Vietnamese specific interests
    agriculture = Column(Float, nullable=True)  # Nông nghiệp
    manufacturing = Column(Float, nullable=True)  # Sản xuất
    technology_it = Column(Float, nullable=True)  # Công nghệ thông tin
    business_finance = Column(Float, nullable=True)  # Kinh doanh, tài chính
    education = Column(Float, nullable=True)  # Giáo dục
    healthcare = Column(Float, nullable=True)  # Y tế
    media_communication = Column(Float, nullable=True)  # Truyền thông
    public_service = Column(Float, nullable=True)  # Dịch vụ công
    tourism = Column(Float, nullable=True)  # Du lịch
    interest_responses = Column(JSON, nullable=True)  # Raw interest responses
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relationship
    user = relationship("User", back_populates="interest_assessments")