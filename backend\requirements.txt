# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.12.1

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# Redis for caching and rate limiting
redis==5.0.1
hiredis==2.2.3

# Machine Learning
scikit-learn==1.3.2
tensorflow==2.15.0
numpy==1.24.3
pandas==2.1.4

# Background tasks
celery==5.3.4
kombu==5.3.4

# HTTP client
httpx==0.25.2
requests==2.31.0

# File handling
python-magic==0.4.27
Pillow==10.1.0

# Environment and configuration
python-dotenv==1.0.0

# Logging and monitoring
structlog==23.2.0

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Email (for notifications)
fastapi-mail==1.4.1

# CORS and middleware
starlette==0.27.0

# Date and time utilities
python-dateutil==2.8.2

# JSON handling
orjson==3.9.10

# Rate limiting
slowapi==0.1.9

# File uploads and validation
aiofiles==23.2.1

# Vietnamese text processing (optional)
underthesea==6.7.0

# Monitoring and health checks
psutil==5.9.6

# Development server
watchfiles==0.21.0
