# System Workflow

## Overview

This document describes the workflow of the AI-powered career matching system, from user registration to receiving personalized career recommendations.

## Workflow Diagram

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant ML
    participant Database
    participant Cache
    participant TaskQueue
    
    User->>Frontend: Access Platform
    Frontend->>Backend: Register/Create Account
    Backend->>Database: Store User Data
    Backend->>Cache: Create User Session
    
    User->>Frontend: Complete Personality Assessment
    Frontend->>Backend: Submit Assessment Data
    Backend->>Database: Store Assessment Results
    Backend->>TaskQueue: Queue Assessment Processing
    
    TaskQueue->>ML: Process Personality Data
    ML->>ML: Calculate Personality Traits
    ML->>TaskQueue: Return Trait Scores
    
    User->>Frontend: Complete Skills Evaluation
    Frontend->>Backend: Submit Skills Data
    Backend->>Database: Store Skills Data
    Backend->>TaskQueue: Queue Skills Analysis
    
    TaskQueue->>ML: Process Skills Data
    ML->>ML: Analyze Academic Performance
    ML->>TaskQueue: Return Skills Profile
    
    User->>Frontend: Complete Interest Inventory
    Frontend->>Backend: Submit Interest Data
    Backend->>Database: Store Interest Data
    
    User->>Frontend: Request Career Recommendations
    Frontend->>Backend: Generate Recommendations Request
    Backend->>ML: Compile User Profile
    ML->>ML: Run Recommendation Algorithm
    ML->>Backend: Return Career Matches
    
    Backend->>Database: Store Recommendations
    Backend->>Frontend: Return Recommendations
    Frontend->>User: Display Career Matches
    
    User->>Frontend: View Career Profile
    Frontend->>Backend: Request Career Details
    Backend->>Database: Fetch Career Information
    Backend->>Frontend: Return Career Details
    Frontend->>User: Display Career Profile
    
    User->>Frontend: Subscribe to Premium
    Frontend->>Backend: Process Payment Request
    Backend->>Database: Store Payment Info
    Backend->>User: Confirm Subscription
```

## Detailed Workflow Steps

### 1. User Registration
- User accesses the platform
- Creates an account with email/phone
- System validates credentials
- User profile is created in database
- Session is established in Redis

### 2. Personality Assessment
- User completes Big 5 personality test
- Questions adapted for Vietnamese culture
- Responses stored in database
- Background task queued for processing
- Personality traits calculated
- Trait scores stored in user profile

### 3. Skills Evaluation
- User inputs academic performance
- System maps grades to skill levels
- Skills data stored in database
- Background analysis of strengths
- Skills profile generated

### 4. Interest Inventory
- User selects areas of interest
- 200+ career options presented
- Interest selections stored
- Interest patterns identified

### 5. Recommendation Generation
- System compiles complete user profile
- Machine learning model processes data
- Career matches calculated with percentages
- Results stored in database
- Recommendations returned to user

### 6. Career Exploration
- User views recommended careers
- Detailed profiles displayed
- Vietnam-specific information shown
- Salary data and job market trends
- Study pathway recommendations

### 7. Skill Development
- System identifies skill gaps
- Personalized development suggestions
- Progress tracking features
- Achievement milestones

### 8. Parental Involvement
- Parent accounts linked to student profiles
- Progress reports generated
- Career discussion prompts
- Guidance resources for parents

## Background Processes

### Data Synchronization
- Job market data updated regularly
- University requirements synchronized
- Salary surveys processed
- New career options added

### Model Training
- Recommendation models retrained
- Personality assessment refined
- Skills evaluation improved
- Accuracy metrics tracked

### Reporting
- Usage analytics generated
- Recommendation effectiveness measured
- User feedback processed
- System improvements identified