from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database.connection import get_db
from app.schemas.user import User, UserUpdate, UserCreate
from app.models.user import User as UserModel
from app.core.security import get_password_hash
from datetime import datetime

router = APIRouter()

@router.get("/", response_model=List[User])
def read_users(
    skip: int = 0, 
    limit: int = 100, 
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """
    Retrieve users.
    """
    query = db.query(UserModel)
    
    if active_only:
        query = query.filter(UserModel.is_active == True)
    
    users = query.offset(skip).limit(limit).all()
    return users

@router.get("/me", response_model=User)
def read_user_me(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    Get current user.
    """
    return current_user

@router.get("/{user_id}", response_model=User)
def read_user(
    user_id: int,
    db: Session = Depends(get_db)
):
    """
    Get user by ID.
    """
    db_user = db.query(UserModel).filter(UserModel.id == user_id).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return db_user

@router.put("/me", response_model=User)
def update_user_me(
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    Update current user.
    """
    # Update user fields
    for field, value in user_update.dict(exclude_unset=True).items():
        setattr(current_user, field, value)
    
    current_user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(current_user)
    
    return current_user

@router.put("/{user_id}", response_model=User)
def update_user(
    user_id: int,
    user_update: UserUpdate,
    db: Session = Depends(get_db)
):
    """
    Update a user.
    """
    db_user = db.query(UserModel).filter(UserModel.id == user_id).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Update user fields
    for field, value in user_update.dict(exclude_unset=True).items():
        setattr(db_user, field, value)
    
    db_user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_user)
    
    return db_user

@router.delete("/{user_id}")
def delete_user(
    user_id: int,
    db: Session = Depends(get_db)
):
    """
    Delete a user.
    """
    db_user = db.query(UserModel).filter(UserModel.id == user_id).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Instead of deleting, mark as inactive
    db_user.is_active = False
    db.commit()
    
    return {"message": "User deactivated successfully"}

@router.get("/search/", response_model=List[User])
def search_users(
    query: str = Query(..., min_length=1),
    db: Session = Depends(get_db)
):
    """
    Search users by username or email.
    """
    users = db.query(UserModel).filter(
        (UserModel.username.contains(query)) | 
        (UserModel.email.contains(query)) |
        (UserModel.full_name.contains(query))
    ).limit(20).all()
    
    return users

# Dependency function (should be in a separate file in a real application)
def get_current_user(db: Session = Depends(get_db)):
    # This is a placeholder - in a real application, you would verify the token
    # and return the current user
    return db.query(UserModel).first()