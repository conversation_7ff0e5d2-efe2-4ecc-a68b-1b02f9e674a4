from datetime import datetime, timedelta
from typing import Optional, Union, Any
from jose import J<PERSON><PERSON><PERSON><PERSON>, jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import Depends, HTTPException, status
from fastapi.security import OA<PERSON>2<PERSON><PERSON><PERSON><PERSON>earer
from sqlalchemy.orm import Session
import logging

from app.core.config import settings
from app.database.connection import get_db
from app.models.user import User
from app.core.exceptions import AuthenticationError, AuthorizationError

logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/api/v1/auth/login",
    scheme_name="JWT"
)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a plain password against its hash.
    
    Args:
        plain_password: The plain text password
        hashed_password: The hashed password
        
    Returns:
        bool: True if password matches, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """
    Hash a password.
    
    Args:
        password: The plain text password
        
    Returns:
        str: The hashed password
    """
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token.
    
    Args:
        data: The data to encode in the token
        expires_delta: Token expiration time
        
    Returns:
        str: The encoded JWT token
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=getattr(settings, 'ACCESS_TOKEN_EXPIRE_MINUTES', 30)
        )
    
    to_encode.update({"exp": expire})
    
    secret_key = getattr(settings, 'SECRET_KEY', 'your-secret-key-here')
    algorithm = getattr(settings, 'ALGORITHM', 'HS256')
    
    encoded_jwt = jwt.encode(to_encode, secret_key, algorithm=algorithm)
    return encoded_jwt

def create_refresh_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT refresh token.
    
    Args:
        data: The data to encode in the token
        expires_delta: Token expiration time
        
    Returns:
        str: The encoded JWT refresh token
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            days=getattr(settings, 'REFRESH_TOKEN_EXPIRE_DAYS', 7)
        )
    
    to_encode.update({"exp": expire, "type": "refresh"})
    
    secret_key = getattr(settings, 'SECRET_KEY', 'your-secret-key-here')
    algorithm = getattr(settings, 'ALGORITHM', 'HS256')
    
    encoded_jwt = jwt.encode(to_encode, secret_key, algorithm=algorithm)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """
    Verify and decode a JWT token.
    
    Args:
        token: The JWT token to verify
        
    Returns:
        dict: The decoded token payload, or None if invalid
    """
    try:
        secret_key = getattr(settings, 'SECRET_KEY', 'your-secret-key-here')
        algorithm = getattr(settings, 'ALGORITHM', 'HS256')
        
        payload = jwt.decode(token, secret_key, algorithms=[algorithm])
        return payload
    except JWTError as e:
        logger.warning(f"Token verification failed: {e}")
        return None

def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> User:
    """
    Get the current authenticated user from JWT token.
    
    Args:
        token: The JWT token
        db: Database session
        
    Returns:
        User: The authenticated user
        
    Raises:
        AuthenticationError: If token is invalid or user not found
    """
    credentials_exception = AuthenticationError("Could not validate credentials")
    
    try:
        payload = verify_token(token)
        if payload is None:
            raise credentials_exception
            
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
            
    except JWTError:
        raise credentials_exception
    
    user = db.query(User).filter(User.username == username).first()
    if user is None:
        raise credentials_exception
        
    if not user.is_active:
        raise AuthenticationError("User account is disabled")
    
    return user

def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get the current active user.
    
    Args:
        current_user: The current user from token
        
    Returns:
        User: The active user
        
    Raises:
        AuthenticationError: If user is not active
    """
    if not current_user.is_active:
        raise AuthenticationError("User account is disabled")
    return current_user

def get_current_admin_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get the current admin user.
    
    Args:
        current_user: The current user from token
        
    Returns:
        User: The admin user
        
    Raises:
        AuthorizationError: If user is not an admin
    """
    if not current_user.is_admin:
        raise AuthorizationError("Admin access required")
    return current_user

def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
    """
    Authenticate a user with username and password.
    
    Args:
        db: Database session
        username: The username
        password: The plain text password
        
    Returns:
        User: The authenticated user, or None if authentication fails
    """
    user = db.query(User).filter(User.username == username).first()
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user

def check_user_permissions(user: User, required_permissions: list) -> bool:
    """
    Check if user has required permissions.
    
    Args:
        user: The user to check
        required_permissions: List of required permissions
        
    Returns:
        bool: True if user has all required permissions
    """
    if user.is_admin:
        return True
    
    # For now, we'll implement basic role-based access
    # This can be extended with more granular permissions later
    user_permissions = []
    
    if user.is_premium:
        user_permissions.extend(['premium_features', 'detailed_recommendations'])
    
    user_permissions.extend(['basic_features', 'assessments', 'recommendations'])
    
    return all(perm in user_permissions for perm in required_permissions)

def require_permissions(required_permissions: list):
    """
    Decorator to require specific permissions for an endpoint.
    
    Args:
        required_permissions: List of required permissions
        
    Returns:
        Dependency function
    """
    def permission_checker(current_user: User = Depends(get_current_user)):
        if not check_user_permissions(current_user, required_permissions):
            raise AuthorizationError(
                f"Required permissions: {', '.join(required_permissions)}"
            )
        return current_user
    
    return permission_checker

def generate_password_reset_token(user_id: int) -> str:
    """
    Generate a password reset token.
    
    Args:
        user_id: The user ID
        
    Returns:
        str: The password reset token
    """
    data = {
        "sub": str(user_id),
        "type": "password_reset"
    }
    expires_delta = timedelta(hours=1)  # Reset token expires in 1 hour
    return create_access_token(data, expires_delta)

def verify_password_reset_token(token: str) -> Optional[int]:
    """
    Verify a password reset token and return user ID.
    
    Args:
        token: The password reset token
        
    Returns:
        int: The user ID if token is valid, None otherwise
    """
    payload = verify_token(token)
    if payload is None:
        return None
    
    if payload.get("type") != "password_reset":
        return None
    
    try:
        user_id = int(payload.get("sub"))
        return user_id
    except (ValueError, TypeError):
        return None

def create_api_key(user_id: int, name: str = "default") -> str:
    """
    Create an API key for a user.
    
    Args:
        user_id: The user ID
        name: Name for the API key
        
    Returns:
        str: The API key
    """
    data = {
        "sub": str(user_id),
        "type": "api_key",
        "name": name
    }
    # API keys don't expire by default
    return create_access_token(data, expires_delta=timedelta(days=365))

def verify_api_key(api_key: str, db: Session) -> Optional[User]:
    """
    Verify an API key and return the associated user.
    
    Args:
        api_key: The API key to verify
        db: Database session
        
    Returns:
        User: The user associated with the API key, or None if invalid
    """
    payload = verify_token(api_key)
    if payload is None:
        return None
    
    if payload.get("type") != "api_key":
        return None
    
    try:
        user_id = int(payload.get("sub"))
        user = db.query(User).filter(User.id == user_id).first()
        return user if user and user.is_active else None
    except (ValueError, TypeError):
        return None
