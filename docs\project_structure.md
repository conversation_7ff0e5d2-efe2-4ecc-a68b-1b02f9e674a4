# Project Structure

## Overview

This document describes the directory structure and organization of the AI-powered career matching system.

## Directory Structure

```
ai-career-matching/
├── backend/
│   ├── app/
│   │   ├── api/
│   │   │   ├── v1/
│   │   │   │   ├── auth.py
│   │   │   │   ├── users.py
│   │   │   │   ├── assessments.py
│   │   │   │   ├── careers.py
│   │   │   │   ├── recommendations.py
│   │   │   │   └── payments.py
│   │   │   └── __init__.py
│   │   ├── models/
│   │   │   ├── user.py
│   │   │   ├── assessment.py
│   │   │   ├── career.py
│   │   │   ├── recommendation.py
│   │   │   └── payment.py
│   │   ├── schemas/
│   │   │   ├── user.py
│   │   │   ├── assessment.py
│   │   │   ├── career.py
│   │   │   ├── recommendation.py
│   │   │   └── payment.py
│   │   ├── database/
│   │   │   ├── __init__.py
│   │   │   ├── connection.py
│   │   │   └── session.py
│   │   ├── utils/
│   │   │   ├── auth.py
│   │   │   ├── cache.py
│   │   │   ├── rate_limit.py
│   │   │   └── helpers.py
│   │   ├── core/
│   │   │   ├── config.py
│   │   │   ├── logging.py
│   │   │   └── exceptions.py
│   │   └── main.py
│   ├── ml/
│   │   ├── personality_model.py
│   │   ├── skills_model.py
│   │   ├── interest_model.py
│   │   ├── recommendation_engine.py
│   │   ├── data_preprocessing.py
│   │   └── model_trainer.py
│   ├── celery_worker/
│   │   ├── tasks.py
│   │   └── worker.py
│   ├── tests/
│   │   ├── test_api/
│   │   ├── test_models/
│   │   ├── test_ml/
│   │   └── conftest.py
│   ├── requirements.txt
│   ├── Dockerfile
│   └── docker-compose.yml
├── frontend/
│   ├── public/
│   │   ├── index.html
│   │   ├── favicon.ico
│   │   └── manifest.json
│   ├── src/
│   │   ├── components/
│   │   │   ├── assessment/
│   │   │   ├── career/
│   │   │   ├── profile/
│   │   │   ├── recommendations/
│   │   │   └── ui/
│   │   ├── views/
│   │   │   ├── Home.vue
│   │   │   ├── Login.vue
│   │   │   ├── Register.vue
│   │   │   ├── Assessment.vue
│   │   │   ├── Results.vue
│   │   │   ├── CareerDetail.vue
│   │   │   └── Profile.vue
│   │   ├── assets/
│   │   │   ├── images/
│   │   │   ├── icons/
│   │   │   └── styles/
│   │   ├── locales/
│   │   │   ├── vi.json
│   │   │   └── en.json
│   │   ├── store/
│   │   │   ├── index.js
│   │   │   └── modules/
│   │   ├── router/
│   │   │   └── index.js
│   │   ├── services/
│   │   │   ├── api.js
│   │   │   └── auth.js
│   │   ├── utils/
│   │   │   └── helpers.js
│   │   ├── App.vue
│   │   └── main.js
│   ├── package.json
│   ├── vue.config.js
│   └── babel.config.js
├── data/
│   ├── vietnamese_careers.json
│   ├── personality_questions.json
│   ├── skills_mapping.json
│   ├── interest_categories.json
│   └── job_market_data.json
├── docs/
│   ├── architecture.md
│   ├── workflow.md
│   ├── project_structure.md
│   └── api_documentation.md
├── scripts/
│   ├── setup.sh
│   ├── deploy.sh
│   └── backup.sh
├── .github/
│   └── workflows/
│       ├── ci.yml
│       └── cd.yml
├── .gitignore
├── README.md
├── LICENSE
└── requirements.txt
```

## Backend Structure

### API Layer
- RESTful endpoints organized by resource
- Versioned API (v1)
- Authentication and authorization
- Request/response validation

### Models Layer
- Database models using SQLAlchemy
- User, assessment, career, recommendation, payment models
- Relationship definitions
- CRUD operations

### Schemas Layer
- Pydantic models for request/response validation
- Data serialization/deserialization
- Input validation rules

### Database Layer
- Database connection management
- Session handling
- Migration scripts

### Utils Layer
- Authentication utilities
- Caching with Redis
- Rate limiting implementation
- Helper functions

### Core Layer
- Configuration management
- Logging setup
- Custom exception definitions

### ML Layer
- Personality assessment model
- Skills evaluation model
- Interest analysis model
- Recommendation engine
- Data preprocessing utilities
- Model training scripts

### Celery Worker
- Background task processing
- Asynchronous job execution
- Task definitions

## Frontend Structure

### Components
- Reusable UI components
- Assessment-specific components
- Career information display
- Profile management
- UI elements following Material Design

### Views
- Page-level components
- Route-specific views
- User interface flows

### Assets
- Images and icons
- Stylesheets
- Static resources

### Locales
- Vietnamese language files
- English language files
- Translation management

### Store
- State management (Vuex)
- Global state definitions
- Module-based organization

### Router
- Client-side routing
- Route protection
- Navigation guards

### Services
- API communication layer
- Authentication services
- External service integrations

## Data Directory

Contains static data files:
- Vietnamese career information
- Personality assessment questions
- Skills mapping to academic subjects
- Interest categories and descriptions
- Job market data and trends

## Documentation

- Architecture diagrams
- Workflow explanations
- Project structure documentation
- API documentation

## Scripts

- Setup scripts for development environment
- Deployment automation
- Backup and maintenance scripts

## CI/CD

- GitHub Actions workflows
- Continuous integration setup
- Continuous deployment configuration