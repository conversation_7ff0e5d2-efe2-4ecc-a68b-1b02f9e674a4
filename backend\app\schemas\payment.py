from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from enum import Enum

class PaymentStatus(str, Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"

class PaymentMethod(str, Enum):
    MOMO = "momo"
    ZALOPAY = "zalopay"
    VIETTELPAY = "viettelpay"
    BANK_TRANSFER = "bank_transfer"
    CASH = "cash"

class SubscriptionTier(str, Enum):
    FREE = "free"
    BASIC = "basic"
    PREMIUM = "premium"

class PaymentBase(BaseModel):
    amount: float
    currency: str = "VND"
    payment_method: PaymentMethod
    description: Optional[str] = None
    subscription_tier: SubscriptionTier

class PaymentCreate(PaymentBase):
    pass

class PaymentUpdate(BaseModel):
    payment_status: Optional[PaymentStatus] = None
    transaction_id: Optional[str] = None
    completed_at: Optional[datetime] = None

class PaymentInDBBase(PaymentBase):
    id: int
    user_id: int
    payment_status: PaymentStatus
    transaction_id: Optional[str] = None
    momo_request_id: Optional[str] = None
    zalopay_app_trans_id: Optional[str] = None
    viettelpay_transaction_id: Optional[str] = None
    bank_transfer_reference: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None

    class Config:
        orm_mode = True

class Payment(PaymentInDBBase):
    pass

class SubscriptionBase(BaseModel):
    tier: SubscriptionTier
    start_date: datetime
    end_date: datetime
    is_active: bool = True
    max_recommendations: int = 5
    can_export_reports: bool = False
    priority_support: bool = False

class SubscriptionCreate(SubscriptionBase):
    user_id: int
    payment_id: Optional[int] = None

class SubscriptionUpdate(BaseModel):
    tier: Optional[SubscriptionTier] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_active: Optional[bool] = None
    max_recommendations: Optional[int] = None
    can_export_reports: Optional[bool] = None
    priority_support: Optional[bool] = None

class SubscriptionInDBBase(SubscriptionBase):
    id: int
    user_id: int
    payment_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class Subscription(SubscriptionInDBBase):
    pass

class PaymentRequest(BaseModel):
    amount: float
    payment_method: PaymentMethod
    subscription_tier: SubscriptionTier
    description: Optional[str] = None

class PaymentResponse(BaseModel):
    payment_id: int
    payment_url: Optional[str] = None  # For redirecting to payment gateway
    status: PaymentStatus
    message: str

class PaymentCallback(BaseModel):
    transaction_id: str
    status: PaymentStatus
    amount: float
    currency: str = "VND"
    payment_method: PaymentMethod
    signature: str  # For verifying authenticity

class SubscriptionCheck(BaseModel):
    user_id: int
    has_active_subscription: bool
    current_tier: Optional[SubscriptionTier] = None
    subscription_end_date: Optional[datetime] = None
    remaining_recommendations: int

class PaymentHistoryRequest(BaseModel):
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    payment_status: Optional[PaymentStatus] = None
    payment_method: Optional[PaymentMethod] = None

class PaymentHistoryResponse(BaseModel):
    payments: List[Payment]
    total_count: int
    total_amount: float